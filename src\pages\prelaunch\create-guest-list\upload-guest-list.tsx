import { useState, useRef } from 'react';
import { TickCircle, Notepad2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import scan from '../../../assets/animations/scanniing.gif';
import { toast } from 'react-toastify';
import { GuestList, ParsedGuest } from '../../../lib/services/guest-list';
import { useEventStore } from '../../../lib/store/event';
import { CSVConfirmationModal } from '../../../components/modals/CSVConfirmationModal';
import { SingleGuestWarningModal } from '../../../components/modals/SingleGuestWarningModal';
import { useGuestList } from '../../../lib/contexts/GuestListContext';

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface UploadGuestListProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
  onGuestsChange?: (guests: Guest[]) => void;
}

export const UploadGuestList = ({
  onNextStep,
  onGuestsChange,
}: UploadGuestListProps) => {
  const { selectedEvent } = useEventStore();
  const {
    guests: contextGuests,
    setGuests: setContextGuests,
    guestSource,
  } = useGuestList();
  const [fileName, setFileName] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [parsedGuests, setParsedGuests] = useState<Guest[]>(
    guestSource === 'upload' ? contextGuests : []
  );
  const [recordCount, setRecordCount] = useState<number>(0);
  const [errorCount, setErrorCount] = useState<number>(0);
  const [showConfirmationModal, setShowConfirmationModal] =
    useState<boolean>(false);
  const [showSingleGuestWarningModal, setShowSingleGuestWarningModal] =
    useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isValidFileType = (file: File): boolean => {
    const validTypes = [
      '.csv',
      '.xlsx',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];
    return validTypes.some(
      (type) => file.name.toLowerCase().endsWith(type) || file.type === type
    );
  };

  const transformParsedGuestsToUIFormat = (
    parsedGuests: ParsedGuest[]
  ): Guest[] => {
    return parsedGuests.map((guest, index) => ({
      id: Date.now() + index,
      firstName: guest.first_name,
      lastName: guest.last_name,
      email: guest.email,
      phone: guest.phone_number,
    }));
  };

  const getErrorCount = (
    parseErrors: Record<string, string> | string[] | undefined
  ): number => {
    if (!parseErrors) return 0;

    if (Array.isArray(parseErrors)) {
      return parseErrors.length;
    }
    return Object.keys(parseErrors).length;
  };

  const processFile = async (selectedFile: File) => {
    setIsScanning(true);
    setIsComplete(false);
    setErrorCount(0);
    setRecordCount(0);
    setParsedGuests([]);

    try {
      if (!selectedEvent?.id) {
        throw new Error('No event selected. Please select an event first.');
      }

      const result = await GuestList.parseGuestListFile({
        eventId: selectedEvent.id,
        file: selectedFile,
      });

      const errorCount = getErrorCount(result.parse_errors);

      if (result.guests.length === 0) {
        setRecordCount(0);
        setErrorCount(errorCount);
        setParsedGuests([]);
        setIsComplete(true);
        if (onGuestsChange) {
          onGuestsChange([]);
        }
        return;
      }

      const uiGuests = transformParsedGuestsToUIFormat(result.guests);

      // Always set the parsed data and complete state
      setParsedGuests(uiGuests);
      setContextGuests(uiGuests, 'upload');
      setRecordCount(result.guests.length);
      setErrorCount(errorCount);
      setIsComplete(true);
      if (onGuestsChange) {
        onGuestsChange(uiGuests);
      }
    } catch (error) {
      console.error('File processing error:', error);
      setIsComplete(false);
      setParsedGuests([]);
      setRecordCount(0);
      setErrorCount(0);
    } finally {
      setIsScanning(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (!isValidFileType(selectedFile)) {
        toast.error('Invalid file type. Please upload a CSV or XLSX file.');
        setErrorCount(0);
        setRecordCount(0);
        setParsedGuests([]);
        setIsComplete(false);
        if (onGuestsChange) {
          onGuestsChange([]);
        }
        return;
      }

      setFileName(selectedFile.name);
      processFile(selectedFile);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      if (!isValidFileType(droppedFile)) {
        toast.error('Invalid file type. Please upload a CSV or XLSX file.');
        setErrorCount(0);
        setRecordCount(0);
        setParsedGuests([]);
        setIsComplete(false);
        if (onGuestsChange) {
          onGuestsChange([]);
        }
        return;
      }
      setFileName(droppedFile.name);
      processFile(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleNext = () => {
    if (isComplete && parsedGuests.length > 0) {
      // Check if there are errors that need user confirmation
      if (errorCount > 0) {
        // Show confirmation modal instead of proceeding directly
        setShowConfirmationModal(true);
        return;
      }

      // Check if only one guest record, show warning modal
      if (parsedGuests.length === 1) {
        setShowSingleGuestWarningModal(true);
        return;
      }

      // No errors and more than one guest, proceed normally
      if (onNextStep) {
        onNextStep();
      }
    }
  };

  const handleDownloadTemplate = () => {
    const templateUrl = '/template.xlsx';
    const link = document.createElement('a');
    link.href = templateUrl;
    link.download = 'template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Modal handler functions
  const handleModalClose = () => {
    setShowConfirmationModal(false);
  };

  const handleUploadNewDocument = () => {
    // Reset upload state and allow user to select new file
    setShowConfirmationModal(false);
    setFileName('');
    setIsComplete(false);
    setParsedGuests([]);
    setRecordCount(0);
    setErrorCount(0);
    if (onGuestsChange) {
      onGuestsChange([]);
    }
    // Trigger file input click to allow new file selection
    fileInputRef.current?.click();
  };

  const handleProceedWithRecords = () => {
    // Close modal and proceed with normal upload flow
    setShowConfirmationModal(false);

    // Continue with the next step since data is already set
    if (onNextStep) {
      onNextStep();
    }
  };

  const handleSingleGuestWarningClose = () => {
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningAddToQueue = () => {
    // For upload, we can't add more to queue, so just close the modal
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningContinue = () => {
    setShowSingleGuestWarningModal(false);
    // Proceed with the single guest record
    if (onNextStep) {
      onNextStep();
    }
  };

  return (
    <div className="flex-1  pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">Upload Guest list</h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        Got a documented list? upload right away!
      </p>

      <div className="mb-4 text-xs">
        <div className="bg-primary-150 pl-2 pr-1 py-1.5  rounded-2xl  flex gap-0.5 items-center">
          <span className="text-primary-500 font-medium text-[11px]">
            Download the provided CSV template to upload your guest list. Custom
            formats aren't supported{' '}
          </span>
          <button
            onClick={handleDownloadTemplate}
            className=" text-primary text-xs text-nowrap flex items-center gap-0.5 rounded-full py-0.5 px-1 bg-white border border-primary-950 italic font-bold">
            Download Template
            <Icon name="helpCircle" />
          </button>
        </div>
      </div>
      <div
        className={` ${
          isComplete ? 'bg-primary-150' : 'bg-grey-350'
        } flex flex-col md:flex-row rounded-2xl  mb-2 cursor-pointer`}
        onClick={handleUploadClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}>
        <div
          className={` ${
            isComplete ? 'md:bg-primary-250' : 'md:bg-grey-850'
          } px-2 h-full mx-auto py-4 rounded-l-2xl`}>
          <Notepad2
            size={90}
            color={isComplete ? '#B8BBFA' : '#B3B3B3'}
            variant="Bulk"
            className="opacity-70"
          />
        </div>
        <div className="flex flex-col justify-between  w-full text-center md:text-start">
          <div className="md:ml-4 my-6">
            <h3
              className={`italic text-base font-medium ${
                isComplete ? 'text-primary-750' : 'text-black'
              }`}>
              {isComplete ? <> {fileName}</> : ' No file Uploaded yet '}
            </h3>
            <p
              className={`text-xs ${
                isComplete ? 'text-primary-500' : 'text-grey-550 '
              }`}>
              {isComplete
                ? 'Click to upload or change document'
                : 'Click to upload document'}
            </p>
          </div>
          <p
            className={`text-[10px] italic  py-[7px] rounded-br-2xl  ${
              isComplete
                ? 'text-primary-500 bg-primary-250'
                : 'text-dark-200 md:bg-grey-850'
            }`}>
            <span className="font-extrabold">Note:</span> Acceptable docs
            include (.CSV, .XLSX)
          </p>
        </div>
      </div>

      <input
        type="file"
        accept=".csv, .xlsx"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />

      {isScanning && (
        <div className="flex items-center gap-2 border border-grey-150 w-fit py-1 px-3 rounded-full">
          <img src={scan} alt="scanning" className="h-4 w-4" />
          <p className="text-grey-500 text-sm font-medium">
            Scanning Document...
          </p>
        </div>
      )}

      {isComplete && (
        <div className="flex items-center gap-2 italic font-semibold text-sm">
          <p className="text-grey-550 py-1 px-3 rounded-full bg-grey-850">
            {recordCount} Record{recordCount !== 1 ? 's' : ''} Found
          </p>
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-full ${
              errorCount === 0
                ? 'text-green-600 bg-green-50'
                : 'text-orange-600 bg-orange-50'
            }`}>
            <TickCircle
              size={16}
              color={errorCount === 0 ? '#22C55E' : '#EA580C'}
              variant="Bold"
            />
            <span className="text-sm font-medium">
              {errorCount === 0
                ? 'No errors found'
                : `${errorCount} error${errorCount !== 1 ? 's' : ''} found`}
            </span>
          </div>
        </div>
      )}

      <div className="mt-38 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleNext}
          disabled={!isComplete || recordCount === 0}
          className={`bg-primary text-white font-semibold py-3 px-6 rounded-full ${
            !isComplete || recordCount === 0
              ? 'opacity-50 cursor-not-allowed'
              : 'cursor-pointer hover:bg-primary/90'
          }`}>
          Upload Guest Record
        </button>
      </div>

      {/* CSV Confirmation Modal */}
      <CSVConfirmationModal
        isOpen={showConfirmationModal}
        onClose={handleModalClose}
        onEnable={handleUploadNewDocument}
        onContinue={handleProceedWithRecords}
      />

      {/* Single Guest Warning Modal */}
      <SingleGuestWarningModal
        isOpen={showSingleGuestWarningModal}
        onClose={handleSingleGuestWarningClose}
        onAddToQueue={handleSingleGuestWarningAddToQueue}
        onContinue={handleSingleGuestWarningContinue}
      />
    </div>
  );
};
