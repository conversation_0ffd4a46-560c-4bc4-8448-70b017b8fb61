// import { Note } from 'iconsax-react';
// import { useNavigate } from 'react-router-dom';
import { PageTitle } from '../../../../components/helmet/helmet';
import { useEffect, useState } from 'react';
import { Note } from 'iconsax-react';
import { SetGiftRegistry } from '../new-user/set-gift-registry';
import { Modal } from '../../../../components/reuseables/prelaunch';
import { motion } from 'motion/react';
import { modalVariants } from '../../../../components/reuseables/animations/animations';
import gift from '../../../../assets/animations/gift.gif';
import gift1 from '../../../../assets/images/gift1.png';

export const GiftRegistry = () => {
  // const navigate = useNavigate();
  const [showGiftRegistry, setShowGiftRegistry] = useState(false);
  const [isFirstModalOpen, setIsFirstModalOpen] = useState(false);
  const [isSecondModalOpen, setIsSecondModalOpen] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const openFirstModal = () => {
    setIsFirstModalOpen(true);
    setIsSecondModalOpen(false);
  };

  const openSecondModal = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(true);
  };

  const closeAllModals = () => {
    setIsFirstModalOpen(false);
    setIsSecondModalOpen(false);
  };

  const navigateToGiftRegistrySetup = () => {
    closeAllModals();
    setShowGiftRegistry(true);
  };
  // const registries = [
  //   {
  //     id: 1,
  //     title: "Oladele's birthday gifts",
  //     giftItems: 8,
  //     cashGifts: 4,
  //   },
  //   {
  //     id: 2,
  //     title: "Oladele's birthday gifts",
  //     giftItems: 8,
  //     cashGifts: 4,
  //   },
  //   {
  //     id: 3,
  //     title: "Oladele's birthday gifts",
  //     giftItems: 8,
  //     cashGifts: 4,
  //   },
  //   {
  //     id: 4,
  //     title: "Oladele's birthday gifts",
  //     giftItems: 8,
  //     cashGifts: 4,
  //   },
  // ];

  // const handleViewRegistry = (id: number) => {
  //   navigate(`/gift-registry/${id}`);
  // };

  return (
    <div className="max-w-[800px] mx-auto pb-12 px-4 md:px-0 min-h-screen">
      <PageTitle
        title="Gift Registry"
        description="Manage your gift registry"
      />
      <div>
        <div className="">
          <h1 className="text-2xl font-semibold">Gift Registry</h1>
          <p className="text-grey-950 text-base">
            Welcome back, how can we help today?
          </p>
        </div>
        <div className="flex flex-col items-center justify-center mb-40 mt-25">
          <Note size={200} variant="Bulk" color="#B8BBFA" />
          <h2 className="text-xl font-medium mb-3 mt-10">No Items Yet</h2>
          <p className="text-gray-500 text-center mb-10">
            You have no items in your gift registry.
            <br />
            Get started by clicking the button below
          </p>

          <button
            type="button"
            onClick={openFirstModal}
            className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90">
            Create Gift Registry
          </button>
        </div>
      </div>

      {/* <div className="">
        <h1 className="text-2xl font-semibold">Gift Registry</h1>
        <p className="text-grey-950 text-base">
          Welcome back, how can we help today?
        </p>
      </div> */}

      {/* {registries.length > 0 ? (
        <div className="mb-40 mt-7">
          {registries.map((registry) => (
            <div
              key={registry.id}
              className="bg-white rounded-2xl shadow-[0px_12px_120px_0px_#5F5F5F0F] mb-6">
              <div className="flex flex-col md:flex-row  items-center gap-4">
                <div className="grid grid-cols-2 gap-2 p-2 md:border-r border-grey-150">
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                  <div className="bg-gray-200 rounded-lg h-18 w-18"></div>
                </div>
                <div className="flex-1 text-center md:text-left">
                  <h2 className="md:text-[28px] font-semibold italic">
                    {registry.title}
                  </h2>
                  <p className="text-base text-grey-950 mb-5">
                    <span className="font-bold"> {registry.giftItems}</span>{' '}
                    Gift Items •{' '}
                    <span className="font-bold"> {registry.cashGifts}</span>{' '}
                    Cash Gifts
                  </p>
                  <button
                    onClick={() => handleViewRegistry(registry.id)}
                    className="text-primary border-primary-250 mb-5 md:mb-0 cursor-pointer rounded-full font-semibold text-sm border px-3.5 py-2">
                    View Gift Registry
                  </button>
                </div>
              </div>
            </div>
          ))}

          <div className="flex justify-center">
            <button
              type="button"
              onClick={() => setIsGiftRegistryModalOpen(true)}
              className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90 mt-6">
              Create Gift Registry
            </button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center mb-40 mt-25">
          <Note size={200} variant="Bulk" color="#B8BBFA" />

          <h2 className="text-xl font-medium mb-3 mt-10">No Items Yet</h2>

          <p className="text-gray-500 text-center mb-10">
            You have no items in your gift registry.
            <br />
            Get started by clicking the button below
          </p>

          <button
            type="button"
            onClick={() => setIsGiftRegistryModalOpen(true)}
            className="bg-primary text-white font-semibold py-3 px-6 rounded-full hover:bg-primary/90">
            Create Gift Registry
          </button>
        </div>
      )} */}
      <Modal
        isOpen={isFirstModalOpen}
        onClose={closeAllModals}
        actionButton={openSecondModal}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="Wish. Share. Receive."
        description={
          <p className="text-grey-950 text-lg">
            Create the perfect wishlist, share it with <br /> loved ones, and
            receive gifts you truly want.
            <br /> No duplicates, no guesswork—just joy!
          </p>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      <Modal
        isOpen={isSecondModalOpen}
        onClose={closeAllModals}
        actionButton={navigateToGiftRegistrySetup}
        forwardActionText="Continue"
        thirdModalAnimation={true}
        title="Gift"
        name="Registry"
        subtitle="WHAT TO EXPECT"
        description={
          <div className="mt-2">
            <p className="mb-5 text-base text-grey-550 font-normal">
              Here are some steps to follow to set up your gift registry
            </p>

            <div className="space-y-4 font-semibold italic">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Setup your account to receive payments{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <p className="text-sm text-dark-blue-400 text-left ">
                  Set up your Wallet{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Enter Delivery details for your gift items{' '}
                </p>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-650 text-white flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <p className="text-sm text-dark-blue-400 text-left">
                  Start curating gifts{' '}
                </p>
              </div>
            </div>
          </div>
        }
        leftContent={
          <div className="relative overflow-hidden rounded-l-[20px] rounded-r-[20px] md:rounded-r-none h-[505px] md:h-full bg-[linear-gradient(179.93deg,#343CD8_0.06%,#000040_39.43%)]">
            <img
              src={gift}
              alt="gift"
              className="h-full w-full object-cover opacity-40"
            />
            <motion.img
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              src={gift1}
              alt="gift decoration"
              className="absolute z-50 top-18 left-0 rounded-l-[20px]  h-full w-full object-contain"
            />
          </div>
        }
        leftContentMobile={true}
      />
      {showGiftRegistry && (
        <SetGiftRegistry onClose={() => setShowGiftRegistry(false)} />
      )}
    </div>
  );
};
