import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  StepProgress,
  Step,
} from "../../../../components/step-progress/step-progress";
import { Footer } from "../../footer";
import { PinEntryModal } from "./pin-entry-modal";
import { WithdrawalPreviewModal } from "./withdrawal-preview-modal";
import {
  Wallet,
  PayoutAccount,
  PayoutBank,
  WalletAPI,
} from "../../../../lib/apis/walletapi";
import { toast } from "react-toastify";

// Bank illustration SVG component for the blue card
const BankIllustrationWhite: React.FC = () => (
  <svg
    width="98"
    height="86"
    viewBox="0 0 98 86"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path opacity="0.6" d="M40 55H20V90H40V55Z" fill="white" />
    <path opacity="0.4" d="M60 55H40V90H60V55Z" fill="white" />
    <path opacity="0.6" d="M80 55H60V90H80V55Z" fill="white" />
    <path opacity="0.4" d="M100 55H80V90H100V55Z" fill="white" />
    <path
      d="M106.85 28.7491L61.85 10.7492C60.85 10.3492 59.15 10.3492 58.15 10.7492L13.15 28.7491C11.4 29.4491 10 31.4991 10 33.3991V49.9991C10 52.7491 12.25 54.9991 15 54.9991H105C107.75 54.9991 110 52.7491 110 49.9991V33.3991C110 31.4991 108.6 29.4491 106.85 28.7491ZM60 42.4991C55.85 42.4991 52.5 39.1491 52.5 34.9991C52.5 30.8491 55.85 27.4991 60 27.4991C64.15 27.4991 67.5 30.8491 67.5 34.9991C67.5 39.1491 64.15 42.4991 60 42.4991Z"
      fill="white"
    />
  </svg>
);

export const WithdrawalAmount: React.FC = () => {
  const [amount, setAmount] = useState<string>("");
  const [showWithdrawalPreview, setShowWithdrawalPreview] = useState(false);
  const [showWithdrawalFlow, setShowWithdrawalFlow] = useState(false);
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState<PayoutAccount | null>(
    null
  );
  const [banks, setBanks] = useState<PayoutBank[]>([]);
  const navigate = useNavigate();
  const location = useLocation();

  const steps: Step[] = [
    { id: 1, name: "Select Account" },
    { id: 2, name: "Authenticate" },
  ];

  useEffect(() => {
    const scrollY = window.scrollY;
    document.body.style.position = "fixed";
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";
    return () => {
      const scrollY = document.body.style.top;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
      window.scrollTo(0, parseInt(scrollY || "0") * -1);
    };
  }, []);

  useEffect(() => {
    const initializePage = async () => {
      // Get selected account and wallet from navigation state
      const state = location.state as {
        selectedAccount?: PayoutAccount;
        wallet?: Wallet;
      } | null;

      if (state?.selectedAccount && state?.wallet) {
        setSelectedAccount(state.selectedAccount);
        setWallet(state.wallet);

        // Load banks to resolve bank names
        try {
          const bankList = await WalletAPI.getPayoutBanks("ngn");
          setBanks(bankList);
        } catch (error) {
          console.error("Failed to load banks:", error);
        }

        setLoading(false);
      } else {
        // If no state, redirect back to account selection
        navigate("/withdrawal/select-account");
      }
    };

    initializePage();
  }, [location.state, navigate]);

  const handleCancel = () => {
    navigate(-1);
  };

  const handleEnterPin = () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    if (!wallet) {
      toast.error("Wallet not found");
      return;
    }

    if (!selectedAccount) {
      toast.error("Selected account not found");
      return;
    }

    // Show withdrawal preview modal first
    setShowWithdrawalPreview(true);
  };

  const handleWithdrawalFlowClose = () => {
    setShowWithdrawalFlow(false);
    navigate("/");
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Remove any non-numeric characters except commas
    const numericValue = value.replace(/[^0-9,]/g, "");
    setAmount(numericValue);
  };

  const isAmountValid = amount.trim().length > 0;

  if (loading) {
    return (
      <div className="fixed inset-0 z-10 overflow-y-auto bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-gray-600">Loading wallet information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto bg-white">
      <div className="flex flex-col w-full font-rethink min-h-screen">
        <div className="fixed top-0 left-0 px-2 sm:px-4 md:px-0 right-0 z-50 max-w-full sm:max-w-[560px] w-full mx-auto">
          <div className="h-[48px] absolute top-[-50px] bg-transparent w-full max-w-[443px] blur-xl [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F] group-scroll:shadow-none group-scroll:bg-white/10 group-scroll:backdrop-blur-md"></div>
          <div className="">
            <div className="flex justify-end pt-8 pb-5 ">
              <button
                onClick={handleCancel}
                className="px-4 sm:px-6 py-2 sm:py-3 cursor-pointer rounded-full bg-primary-250 text-primary font-medium text-sm sm:text-base mr-2 sm:mr-0"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

        <div className="max-w-full sm:max-w-[560px] w-full mx-auto pt-24 sm:pt-32 px-2 sm:px-4 md:px-0 ">
          <h1 className="text-[22px] sm:text-[28px] font-semibold mt-7 sm:ml-3.5">
            Withdraw from Wallet
          </h1>
          <p className="text-base text-grey-950 sm:ml-3.5">
            Move your funds safely to your bank account.
          </p>
        </div>

        {/* Step Progress */}
        <div className="px-2 sm:px-0">
          <StepProgress
            steps={steps}
            activeStep={2}
            completedSteps={[1]}
            onStepChange={() => {}}
          />
        </div>

        {/* Content */}
        <div className="px-2 sm:px-0 sm:ml-3.5">
          <div className="max-w-full sm:max-w-[560px] w-full mx-auto mt-8">
            {/* Selected Account Card */}
            {selectedAccount && (
              <div className="relative p-4 sm:p-6 rounded-2xl bg-[#4D55F2] h-[110px] sm:h-[130px] mb-8">
                <div className="flex items-center gap-2 sm:gap-4">
                  <div>
                    <p className="font-bold text-white text-sm sm:text-base">
                      {selectedAccount.account_name}
                    </p>
                    <p className="text-xs sm:text-sm text-white/90 mt-1">
                      {banks.find(
                        (bank) => bank.bank_code === selectedAccount.bank_code
                      )?.bank_name || selectedAccount.bank_code}
                    </p>
                    <p className="text-xs sm:text-sm text-white/90 mt-1">
                      {selectedAccount.account_number}
                    </p>
                  </div>
                </div>
                <div className="flex-shrink-0 absolute bottom-0 right-0">
                  <BankIllustrationWhite />
                </div>
              </div>
            )}

            {/* Withdrawal Amount */}
            <div className="text-center mb-8">
              <p className="text-[#1A1A1A] font-medium mt-8 sm:mt-10 mb-4 sm:mb-6 text-[18px] sm:text-[22px]">
                How much do you want to Withdraw?
              </p>

              {/* Amount Input Field */}
              <div className="relative mb-8">
                <div className="bg-[#F5F9FF] rounded-2xl p-4 sm:p-6 h-[64px] sm:h-[84px] max-w-full flex items-center justify-between">
                  <input
                    type="text"
                    value={amount}
                    onChange={handleAmountChange}
                    placeholder="Enter Amount"
                    className="bg-transparent text-xl sm:text-2xl font-medium text-grey-50 italic placeholder:italic placeholder:font-normal placeholder-[#717680] outline-none flex-1"
                  />
                  <span className="text-lg sm:text-xl font-normal italic text-black  sm:ml-4">
                    NGN
                  </span>
                </div>
              </div>
            </div>

            <button
              onClick={handleEnterPin}
              disabled={!isAmountValid}
              className={`w-full py-3 sm:py-4 px-4 sm:px-6 rounded-full mt-16 sm:mt-[120px] font-bold text-base sm:text-lg transition-all ${
                isAmountValid
                  ? "bg-primary-50 text-white hover:bg-primary-50/90"
                  : "bg-primary-50/40 text-white cursor-not-allowed"
              }`}
            >
              Continue
            </button>
          </div>
        </div>
      </div>

      <div className="mb-[100px] sm:mb-[150px]"></div>

      {/* Footer */}
      <Footer />

      {/* Withdrawal Preview Modal */}
      {wallet && selectedAccount && (
        <WithdrawalPreviewModal
          isOpen={showWithdrawalPreview}
          onClose={() => setShowWithdrawalPreview(false)}
          onContinue={() => {
            setShowWithdrawalPreview(false);
            setShowWithdrawalFlow(true);
          }}
          amount={parseFloat(amount)}
          wallet={wallet}
          selectedAccount={selectedAccount}
        />
      )}

      {/* PIN Entry Modal */}
      <PinEntryModal
        isOpen={showWithdrawalFlow}
        onClose={handleWithdrawalFlowClose}
        onSuccess={handleWithdrawalFlowClose}
        amount={parseFloat(amount)}
        walletId={wallet?.id || ""}
        selectedAccount={selectedAccount!}
      />
    </div>
  );
};
