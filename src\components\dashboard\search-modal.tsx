import { SearchNormal1, CloseCircle } from "iconsax-react";
import { useState, useEffect } from "react";

interface SearchModalProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  toggleModal: () => void;
}

export const SearchModal = ({
  searchQuery,
  setSearchQuery,
  toggleModal,
}: SearchModalProps) => {
  const [filteredItems, setFilteredItems] = useState<
    Array<{ title: string; category: string }>
  >([]);

  const quickActionItems = [
    { title: "Gift Registry", category: "Gift Registries" },
    { title: "Budget Planner", category: "Quick Actions" },
    { title: "Create Guest List", category: "Quick Actions" },
    { title: "Create Event", category: "Quick Actions" },
    { title: "View Analytics", category: "Quick Actions" },
  ];

  useEffect(() => {
    const filtered = quickActionItems.filter(
      (item) =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredItems(filtered);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  return (
    <div className="hidden fixed inset-0 z-20 bg-black/10 backdrop-blur-[2px] fle items-start justify-center pt-28">
      <div className="w-full max-w-[561px] mx-4">
        <div className="relative flex items-center bg-[#F9FAFB] rounded-[64px] h-[40px] mb-4">
          <SearchNormal1 size={20} color="#667085" />
          <input
            type="text"
            placeholder="Search by event title, registry name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-[#F9FAFB] py-2 pl-3 pr-4 w-full outline-none text-[14px] text-[#101828] placeholder:text-[#667085]"
            autoFocus
          />
          <button onClick={toggleModal} className="mr-4">
            <CloseCircle size={20} className="text-[#667085]" />
          </button>
        </div>

        <div className="bg-white rounded-[8px] p-6">
          <div className="max-h-[400px] overflow-y-auto">
            {filteredItems.length > 0 ? (
              <>
                {Array.from(
                  new Set(filteredItems.map((item) => item.category))
                ).map((category) => (
                  <div key={category} className="mb-6">
                    <h3 className="text-[12px] font-semibold tracking-[0.12em] text-[#667085] uppercase mb-4">
                      {category}
                    </h3>
                    {filteredItems
                      .filter((item) => item.category === category)
                      .map((item, index) => (
                        <div
                          key={index}
                          className="mb-4 hover:bg-[#F9FAFB] rounded-lg cursor-pointer p-3"
                        >
                          <h4 className="font-medium text-[14px] text-[#101828] mb-1">
                            {item.title}
                          </h4>
                          <p className="text-[14px] text-[#667085]">
                            Found in {item.category}
                          </p>
                        </div>
                      ))}
                  </div>
                ))}
              </>
            ) : (
              <div className="text-center py-8 text-[#667085] text-[14px]">
                {searchQuery ? "No results found" : "Start typing to search..."}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
