import { useState, useEffect } from 'react';
import { ArrowDown2 } from 'iconsax-react';
import { FormInput } from '../../../components/inputs/form-input/form-input';
import { GuestPreview } from './guest-preview';
import { Avatar } from '../../../components/reuseables/avatar';
import { SingleGuestWarningModal } from '../../../components/modals/SingleGuestWarningModal';
import { useGuestList } from '../../../lib/contexts/GuestListContext';

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);

    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
};

interface AddGuestManuallyProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
  onGuestsChange?: (guests: Guest[]) => void;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export const AddGuestManually = ({
  onNextStep,
  onFormActiveChange,
  onGuestsChange,
}: AddGuestManuallyProps) => {
  const {
    guests: contextGuests,
    setGuests: setContextGuests,
    guestSource,
  } = useGuestList();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [allGuests, setAllGuests] = useState<Guest[]>(
    guestSource === 'manual' ? contextGuests : []
  );
  const [showPreview, setShowPreview] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [showSingleGuestWarningModal, setShowSingleGuestWarningModal] =
    useState(false);
  const isMobile = useMediaQuery('(max-width: 767px)');

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const cleanPhone = phone.replace(/\D/g, '');
    return cleanPhone.length === 10;
  };

  const isFormValid = (): boolean => {
    return (
      firstName.trim() !== '' &&
      lastName.trim() !== '' &&
      email.trim() !== '' &&
      mobileNumber.trim() !== '' &&
      validateEmail(email) &&
      validatePhone(mobileNumber)
    );
  };

  useEffect(() => {
    const isFormActive =
      firstName !== '' ||
      lastName !== '' ||
      email !== '' ||
      mobileNumber !== '';
    onFormActiveChange?.(isFormActive);
  }, [firstName, lastName, email, mobileNumber, onFormActiveChange]);

  const handleAddToQueue = () => {
    // Clear all previous errors
    setEmailError('');
    setPhoneError('');
    setFirstNameError('');
    setLastNameError('');
    let isValid = true;

    if (!firstName.trim()) {
      setFirstNameError('First name is required');
      isValid = false;
    }

    if (!lastName.trim()) {
      setLastNameError('Last name is required');
      isValid = false;
    }

    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    if (!mobileNumber.trim()) {
      setPhoneError('Phone number is required');
      isValid = false;
    } else if (!validatePhone(mobileNumber)) {
      setPhoneError('Phone number must be exactly 10 digits');
      isValid = false;
    }

    if (isValid) {
      const newGuest: Guest = {
        id: Date.now(),
        firstName,
        lastName,
        email,
        phone: mobileNumber,
      };

      const updatedGuests = [...allGuests, newGuest];
      setAllGuests(updatedGuests);
      setContextGuests(updatedGuests, 'manual');
      onGuestsChange?.(updatedGuests);
      setFirstName('');
      setLastName('');
      setEmail('');
      setMobileNumber('');
      // Clear error states as well
      setFirstNameError('');
      setLastNameError('');
      setEmailError('');
      setPhoneError('');
    }
  };

  const handleAddGuest = () => {
    const hasQueuedGuests = allGuests.length > 0;
    const hasFormData =
      firstName.trim() ||
      lastName.trim() ||
      email.trim() ||
      mobileNumber.trim();

    // If user has queued guests but no form data, proceed directly
    if (hasQueuedGuests && !hasFormData) {
      // Proceed with existing queue
      if (onNextStep) {
        onNextStep();
      }
      return;
    }

    // If user has form data, validate it
    if (hasFormData) {
      // Clear all previous errors
      setEmailError('');
      setPhoneError('');
      setFirstNameError('');
      setLastNameError('');

      let isValid = true;

      if (!firstName.trim()) {
        setFirstNameError('First name is required');
        isValid = false;
      }

      if (!lastName.trim()) {
        setLastNameError('Last name is required');
        isValid = false;
      }

      if (!email.trim()) {
        setEmailError('Email is required');
        isValid = false;
      } else if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        isValid = false;
      }

      if (!mobileNumber.trim()) {
        setPhoneError('Phone number is required');
        isValid = false;
      } else if (!validatePhone(mobileNumber)) {
        setPhoneError('Phone number must be exactly 10 digits');
        isValid = false;
      }

      // If validation fails, don't proceed
      if (!isValid) {
        return;
      }

      // If user has queued guests AND valid form data, add form data to queue first
      if (hasQueuedGuests) {
        const newGuest: Guest = {
          id: Date.now(),
          firstName,
          lastName,
          email,
          phone: mobileNumber,
        };

        const updatedGuests = [...allGuests, newGuest];
        setAllGuests(updatedGuests);
        setContextGuests(updatedGuests, 'manual');
        onGuestsChange?.(updatedGuests);
        setFirstName('');
        setLastName('');
        setEmail('');
        setMobileNumber('');
        setFirstNameError('');
        setLastNameError('');
        setEmailError('');
        setPhoneError('');
        // Then proceed with the updated queue
        if (onNextStep) {
          onNextStep();
        }
        return;
      }

      // If user has valid form data but no queued guests, show warning modal
      if (!hasQueuedGuests) {
        setShowSingleGuestWarningModal(true);
        return;
      }
    }

    // If no form data and no queued guests, do nothing or show appropriate message
    if (!hasFormData && !hasQueuedGuests) {
      // Maybe show a message that they need to add guests first
      return;
    }
  };

  const handleRemoveGuest = (id: number) => {
    const updatedGuests = allGuests.filter((guest: Guest) => guest.id !== id);
    setAllGuests(updatedGuests);
    setContextGuests(updatedGuests, 'manual');
    onGuestsChange?.(updatedGuests);
  };

  const handleAddMoreGuests = () => {
    setShowPreview(false);
  };

  const handleSingleGuestWarningClose = () => {
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningAddToQueue = () => {
    // Add the current form data to queue
    if (isFormValid()) {
      handleAddToQueue();
    }
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningContinue = () => {
    setShowSingleGuestWarningModal(false);
    // Add the guest and proceed to next step
    if (isFormValid()) {
      const newGuest: Guest = {
        id: Date.now(),
        firstName,
        lastName,
        email,
        phone: mobileNumber,
      };

      const updatedGuests = [...allGuests, newGuest];
      setAllGuests(updatedGuests);
      setContextGuests(updatedGuests, 'manual');
      onGuestsChange?.(updatedGuests);

      if (onNextStep) {
        onNextStep();
      }
    }
  };

  if (showPreview) {
    return (
      <GuestPreview
        guests={allGuests}
        onRemoveGuest={handleRemoveGuest}
        onAddMoreGuests={handleAddMoreGuests}
      />
    );
  }

  const guestList = allGuests.map((guest) => ({
    id: guest.id,
    initials: `${guest.firstName.charAt(0)}${guest.lastName.charAt(0)}`,
  }));

  const visibleAvatarCount = isMobile ? 3 : 7;
  const additionalGuests =
    guestList.length > visibleAvatarCount
      ? guestList.length - visibleAvatarCount
      : 0;

  return (
    <div className="flex-1 pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">
        Add guests manually
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        add a guest to your event manually
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          label="Guest First name"
          placeholder="Enter first name"
          value={firstName}
          onChange={(e) => {
            setFirstName(e.target.value);
            if (firstNameError && e.target.value.trim()) {
              setFirstNameError('');
            }
          }}
          error={firstNameError}
        />

        <FormInput
          label="Guest Last name"
          placeholder="Enter last name"
          value={lastName}
          onChange={(e) => {
            setLastName(e.target.value);
            if (lastNameError && e.target.value.trim()) {
              setLastNameError('');
            }
          }}
          error={lastNameError}
        />
      </div>

      <FormInput
        label="Email"
        placeholder="Enter your guest's email"
        type="email"
        value={email}
        onChange={(e) => {
          setEmail(e.target.value);
          if (emailError && validateEmail(e.target.value)) {
            setEmailError('');
          }
        }}
        error={emailError}
      />

      <FormInput
        label="Mobile Number"
        placeholder="Enter mobile number"
        type="tel"
        value={mobileNumber}
        onChange={(e) => {
          const value = e.target.value.replace(/\D/g, '').slice(0, 10);
          setMobileNumber(value);
          if (phoneError && validatePhone(value)) {
            setPhoneError('');
          }
        }}
        error={phoneError}
        leftAddon={
          <div className="flex items-center">
            <span className="text-grey-500 font-semibold mr-1 italic text-base">
              +234
            </span>
            <ArrowDown2 size={16} color="#717680" />
          </div>
        }
      />

      <button
        onClick={handleAddToQueue}
        className="bg-primary-250 hover:bg-primary-250/50 text-primary  text-sm cursor-pointer font-medium py-1 px-3 rounded-full flex items-center mb-2.5">
        <span className="bg-primary text-white text-xs  rounded-full h-3 w-3 flex items-center justify-center mr-2">
          +
        </span>
        Add to Queue
      </button>

      <div className="flex items-center py-2">
        {guestList.slice(0, visibleAvatarCount).map((guest, index) => (
          <Avatar key={guest.id} initials={guest.initials} offset={index > 0} />
        ))}

        {additionalGuests > 0 && (
          <div className="bg-light-blue text-grey-700 w-10 h-10 rounded-full flex items-center justify-center font-medium -ml-2 border-2 border-white">
            +{additionalGuests}
          </div>
        )}
      </div>
      <div className="mt-2.5 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleAddGuest}
          className="bg-primary cursor-pointer text-base font-semibold mr-5 text-white h-12 max-w-[135px] w-full rounded-full hover:bg-primary/80 transition-colors">
          Add Guest
        </button>
      </div>

      <SingleGuestWarningModal
        isOpen={showSingleGuestWarningModal}
        onClose={handleSingleGuestWarningClose}
        onAddToQueue={handleSingleGuestWarningAddToQueue}
        onContinue={handleSingleGuestWarningContinue}
      />
    </div>
  );
};
