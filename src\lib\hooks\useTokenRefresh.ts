/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../services/auth";
import { useUserAuthStore } from "../store/auth";
import { useEventStore } from "../store/event";
import { toast } from "react-toastify";

/**
 * Hook for handling token refresh functionality
 * Provides both automatic and manual token refresh capabilities
 */
export const useTokenRefresh = () => {
  const { refreshToken, setTokens, clearAuthData } = useUserAuthStore();
  const { clearAllEventData } = useEventStore();
  const isRefreshingRef = useRef(false);

  const refreshMutation = useMutation({
    mutationFn: async () => {
      const response = await AuthServices.refreshToken();
      return response.data;
    },
    onSuccess: (data) => {
      if (
        data?.access_token &&
        data?.refresh_token &&
        data?.access_token_expires_at &&
        data?.refresh_token_expires_at
      ) {
        setTokens(
          data.access_token,
          data.refresh_token,
          data.access_token_expires_at,
          data.refresh_token_expires_at
        );
        isRefreshingRef.current = false;
      } else {
        throw new Error("Invalid token response");
      }
    },
    onError: (error: any) => {
      console.error("Token refresh failed:", error);
      isRefreshingRef.current = false;

      // Clear auth data and redirect to login on refresh failure
      clearAuthData();
      clearAllEventData();

      // Show error message
      toast.error("Session expired. Please login again.");

      // Redirect to login after a short delay
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
    },
  });

  /**
   * Manually trigger token refresh
   * Returns a promise that resolves when refresh is complete
   */
  const refreshTokens = useCallback(async (): Promise<boolean> => {
    if (isRefreshingRef.current) {
      return false; // Already refreshing
    }

    if (!refreshToken) {
      console.error("No refresh token available");
      return false;
    }

    isRefreshingRef.current = true;

    try {
      await refreshMutation.mutateAsync();
      return true;
    } catch (error) {
      console.error("Manual token refresh failed:", error);
      return false;
    }
  }, [refreshToken, refreshMutation]);

  return {
    refreshTokens,
    isRefreshing: refreshMutation.isPending || isRefreshingRef.current,
    error: refreshMutation.error,
  };
};
