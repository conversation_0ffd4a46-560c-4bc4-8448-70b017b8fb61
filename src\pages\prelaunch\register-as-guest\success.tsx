import { ArrowRight } from "iconsax-react";
import { useEffect } from "react";
import img from "../../../assets/images/attendace.png";
import successGIF from "../../../assets/animations/gift.gif";
import logo from "../../../assets/icons/Ep-Logo.svg";

export const Success = () => {
  useEffect(() => {
    // Prevent background scrolling when modal is mounted
    document.body.style.position = "fixed";
    document.body.style.width = "100%";
    document.body.style.overflow = "hidden";

    // Cleanup when modal is unmounted
    return () => {
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
    };
  }, []);

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <img src={logo} alt="logo" className="py-7 mx-auto " />
      <img
        src={successGIF}
        alt="gif"
        className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
      />
      <div className="min-h-screen flex items-center justify-center py-12 px-4">
        <div className="relative w-full max-w-[450px]">
          <div className="relative  bg-white border-t border-white rounded-[20px] text-center w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F]">
            {/* Triangle background */}
            <div
              className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] relative rounded-t-[20px] h-[262px] w-full overflow-hidden"
              style={{
                clipPath:
                  "polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)",
              }}
            >
              {" "}
              {/* Image container */}
              <div className="absolute z-0 top-12 sm:top-[-8px] left-0 overflow-hidden">
                <img
                  src={img}
                  alt="Wedding celebration"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            <div className="flex flex-col items-center text-center pt-12 px-4 w-full">
              <div className="max-w-[322px] w-full">
                <h2 className="text-base md:text-2xl md:text-4xl font-medium my-2 text-dark-200 md:text-nowrap">
                  Your Attendance has <br />{" "}
                  <span className="text-base md:text-[32px] text-grey-250">
                    been confirmed ︎
                  </span>
                </h2>
                <p className="text-grey-250 text-sm md:text-base mt-4 mb-7.5">
                  You're all set! Get ready for an amazing event. Grab your
                  invitation card by clicking the button below
                </p>
              </div>
              <button
                type="button"
                // onClick={guestlist}
                className="bg-primary cursor-pointer text-base  text-white flex items-center py-3 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors"
              >
                <span>Download your IV Card</span>
                <div className="rounded-full bg-white/30 p-0.5">
                  <ArrowRight size="16" color="#fff" />
                </div>
              </button>
            </div>
            <div className="flex justify-center mt-5 pb-6">
              <button
                type="button"
                //   onClick={goHome}
                className="bg-grey-110 text-[#FF6630] underline italic font-bold text-sm text-cus-blue-400 py-2.5 px-4 rounded-full cursor-pointer"
              >
                Create your Own Event 🥳{" "}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
