/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Gift,
  Profile2User,
  Home,
  Graph,
  Logout,
  Setting,
  ArrowDown2,
  AddSquare,
  Edit,
  Trash,
} from 'iconsax-react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useUserAuthStore } from '../../lib/store/auth';
import { useMutation } from '@tanstack/react-query';
import { AuthServices } from '../../lib/services/auth';
import { CreatedEventData } from '../../lib/store/event';
import { useEventManagement } from '../../lib/hooks/useEventManagement';
import { events } from '../../lib/services/events';
import { DeleteEventConfirmationModal } from '../modals';
import { toast } from 'react-toastify';

export const Header = () => {
  const { userData, clearAuthData } = useUserAuthStore();
  const {
    userEvents,
    selectedEvent,
    setSelectedEvent,
    clearAllEventData,
    removeEventOptimistically,
  } = useEventManagement();

  const userName = userData?.first_name;
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isEventDropdownOpen, setIsEventDropdownOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const navigate = useNavigate();

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Check if click is outside profile dropdown
      if (
        isProfileDropdownOpen &&
        !target.closest('[data-profile-dropdown]') &&
        !target.closest('[data-profile-trigger]')
      ) {
        setIsProfileDropdownOpen(false);
      }

      // Check if click is outside event dropdown
      if (
        isEventDropdownOpen &&
        !target.closest('[data-event-dropdown]') &&
        !target.closest('[data-event-trigger]')
      ) {
        setIsEventDropdownOpen(false);
      }

      // Check if click is outside mobile menu
      if (
        isMobileMenuOpen &&
        !target.closest('[data-mobile-menu]') &&
        !target.closest('[data-mobile-trigger]')
      ) {
        setIsMobileMenuOpen(false);
      }
    };

    // Only add listener if any dropdown is open
    if (isProfileDropdownOpen || isEventDropdownOpen || isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isProfileDropdownOpen, isEventDropdownOpen, isMobileMenuOpen]);

  const logoutMutation = useMutation({
    mutationFn: () => AuthServices.logout(),
    onSuccess: () => {
      clearAuthData();
      clearAllEventData();
      navigate('/login', { replace: true });
    },
    onError: (error: any) => {
      console.error(error?.response?.data?.message || error?.message);
    },
  });

  const deleteEventMutation = useMutation({
    mutationFn: (eventId: string) => events.deleteEvent(eventId),
    onSuccess: () => {
      if (selectedEvent?.id) {
        removeEventOptimistically(selectedEvent.id);
        toast.success('Event deleted successfully!');
        setIsDeleteModalOpen(false);
        setIsEventDropdownOpen(false);
        navigate('/delete-event-success', {
          state: { eventName: selectedEvent.title },
          replace: true,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to delete event';
      toast.error(errorMessage);
    },
  });

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleEventSelect = (event: CreatedEventData) => {
    setSelectedEvent(event);
    setIsEventDropdownOpen(false);
  };

  const handleDeleteClick = () => {
    if (!selectedEvent) {
      toast.error('No event selected');
      return;
    }
    setIsDeleteModalOpen(true);
    setIsEventDropdownOpen(false);
  };

  const handleDeleteConfirm = () => {
    // if (selectedEvent?.id) {
    //   deleteEventMutation.mutate(selectedEvent.id);
    // }
    navigate('/delete-event-success', {
      state: { eventName: selectedEvent?.title },
      replace: true,
    });
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
  };

  const navigationItems = [
    { title: 'Home', icon: Home, path: '/' },
    { title: 'Guest list', icon: Profile2User, path: '/guest-lists' },
    { title: 'Gift Registry', icon: Gift, path: '/gift-registry' },
    // { title: 'Budget Planner', icon: Graph, path: '/budget-planner' },
    { title: 'Wallet', icon: Graph, path: '/wallet' },
  ];

  return (
    <div className="px-4 md:px-0">
      <div className="absolute inset-0 z-20 max-w-[575px] mx-auto px-6 md:px-0 h-fit">
        <div className="mt-8.5  bg-[#A6A6A60A] relative rounded-full   [box-shadow:0px_33.75px_33.75px_0px_#A6A6A60A,0px_67.49px_84.37px_0px_#A6A6A61A,0px_39.68px_198.42px_0px_#0000000F]">
          <div className="flex bg-white p-2 rounded-full items-center justify-between mb-2 overflow-x-auto ">
            <div className="flex gap-2 items-center">
              <div className="h-8 w-8 bg-white rounded-full flex justify-center items-center">
                {selectedEvent?.banner_preview_url ? (
                  <img
                    src={selectedEvent?.banner_preview_url || ''}
                    alt="event_image"
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-sm text-primary-650 font-bold bg-cus-pink-600 h-8 w-8 flex items-center justify-center rounded-full">
                    {/* {userName?.charAt(0).toUpperCase()}
                    {userData?.last_name?.charAt(0).toUpperCase()} */}
                  </span>
                )}
              </div>
              <div>
                <p className="text-[10px] text-grey-550">Event</p>
                <div
                  data-event-trigger
                  className="flex items-center gap-0.5 cursor-pointer"
                  onClick={() => setIsEventDropdownOpen(!isEventDropdownOpen)}>
                  <p className="italic font-bold text-xs w-[91px] truncate">
                    {selectedEvent?.title || 'Select Event'}
                  </p>
                  <ArrowDown2 size="13" color="#FF5519" />
                </div>
              </div>
            </div>

            <button
              data-mobile-trigger
              className="md:hidden h-12 w-12 bg-white rounded-full flex justify-center items-center"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="w-6 h-6">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={
                    isMobileMenuOpen
                      ? 'M6 18L18 6M6 6l12 12'
                      : 'M4 6h16M4 12h16M4 18h16'
                  }
                />
              </svg>
            </button>

            <div className="hidden md:flex ">
              {navigationItems.map((item) => {
                return (
                  <NavLink
                    key={item.title}
                    to={item.path}
                    className={({ isActive }) =>
                      `px-3 py-[9px] rounded-full text-xs whitespace-nowrap flex items-center gap-1 ${
                        isActive
                          ? 'bg-primary-650 text-white'
                          : 'text-grey-950 hover:bg-gray-100'
                      }`
                    }>
                    <>{item.title}</>
                  </NavLink>
                );
              })}
            </div>

            <div>
              <button
                data-profile-trigger
                onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
                className="h-8 w-8 bg-white rounded-full flex justify-center items-center cursor-pointer">
                {userData?.profile_picture ? (
                  <img
                    src={userData.profile_picture}
                    alt="Profile"
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-sm text-primary-650 font-bold bg-cus-pink-600 h-8 w-8 flex items-center justify-center rounded-full">
                    {userName?.charAt(0).toUpperCase()}
                    {userData?.last_name?.charAt(0).toUpperCase()}
                  </span>
                )}
              </button>
            </div>
          </div>
          {isProfileDropdownOpen && (
            <div
              data-profile-dropdown
              className="absolute right-0  w-[208px] bg-white  border border-grey-850 rounded-2xl [box-shadow:0px_12px_120px_0px_#5F5F5F0F] max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              <div className="px-4 pb-3 pt-5  border-b border-gray-100">
                <p className="text-sm font-semibold text-dark-200 truncate capitalize">
                  {userData?.first_name} {userData?.last_name}
                </p>
                <p className="text-sm text-grey-650 truncate">
                  {userData?.email}
                </p>
              </div>
              <div className="">
                <NavLink
                  to="/settings"
                  onClick={() => setIsProfileDropdownOpen(false)}
                  className="px-4 py-3 text-sm text-gray-700 flex items-center gap-2">
                  <Setting size="16" color="#1A22BF" variant="Bulk" />
                  Settings
                </NavLink>

                <button
                  onClick={handleLogout}
                  className="w-full cursor-pointer px-4 py-4 text-sm rounded-b-2xl text-cus-red bg-cus-pink-700 flex items-center gap-2">
                  <Logout size="16" color="#FF0000" variant="Bulk" />
                  Log out
                </button>
              </div>
            </div>
          )}

          {isMobileMenuOpen && (
            <div
              data-mobile-menu
              className="md:hidden bg-white mt-2 p-2 rounded-lg shadow-lg max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <NavLink
                    key={item.title}
                    to={item.path}
                    className={({ isActive }) =>
                      `px-3 py-2 my-1 rounded-full text-sm font-semibold flex items-center gap-1 ${
                        isActive
                          ? 'bg-primary-650 text-white'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                    onClick={() => setIsMobileMenuOpen(false)}>
                    {({ isActive }) => (
                      <>
                        {isActive ? (
                          <Icon size="22" color="#fff" variant="Bulk" />
                        ) : (
                          <Icon size="22" />
                        )}{' '}
                        {item.title}
                      </>
                    )}
                  </NavLink>
                );
              })}
            </div>
          )}
          {isEventDropdownOpen && (
            <div
              data-event-dropdown
              className="absolute left-0  w-[208px] bg-white  border border-grey-850 rounded-2xl [box-shadow:0px_12px_120px_0px_#5F5F5F0F] max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              <div className="px-4 pb-3 pt-5  border-b border-gray-100">
                <p className="text-sm font-semibold text-dark-200 truncate capitalize">
                  Events
                </p>
                <p className="text-sm text-grey-650 truncate">Switch Events</p>
              </div>
              <div className="max-h-[140px] overflow-y-auto [&::-webkit-scrollbar]:hidden">
                {userEvents.map((event) => (
                  <div
                    key={event.id}
                    onClick={() => handleEventSelect(event)}
                    className={`px-4 py-3 cursor-pointer text-sm text-gray-700 border-b border-gray-100 flex justify-between items-center ${
                      selectedEvent?.id === event.id ? '' : ''
                    }`}>
                    <p className="font-medium truncate">{event.title}</p>
                    {selectedEvent?.id === event.id && (
                      <div className="h-1 w-1 bg-cus-orange rounded-full" />
                    )}
                  </div>
                ))}
                <NavLink
                  to="/edit-event-details"
                  onClick={() => setIsProfileDropdownOpen(false)}
                  className="px-4 py-3 text-sm text-gray-700 flex items-center gap-2  border-b border-gray-100">
                  <Edit size="16" color="#4D55F2" variant="Bulk" />
                  Edit Details
                </NavLink>
                <button
                  type="button"
                  onClick={handleDeleteClick}
                  className="px-4 py-3 text-sm text-gray-700 flex items-center gap-2  border-b border-gray-100">
                  <Trash size="16" color="#4D55F2" variant="Bulk" />
                  Delete Event
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/create-event')}
                  className="w-full cursor-pointer px-4 py-4 text-sm italic rounded-b-2xl text-dark-blue bg-cus-pink-700 flex items-center gap-2">
                  <AddSquare size="16" color="#FF5519" variant="Bulk" />
                  Create New Event
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* <div className="pt-32 ">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 capitalize">
            Hi {userName},
          </h1>
          <p className="text-gray-600">Welcome Back, How can we help today?</p>
        </div>
        <div className="mb-8 ">
          <h2 className="hidden md:block italic font-semibold text-base text-grey-750 mb-3">
            Quick Actions
          </h2>
          <div className="flex justify-between md:hidden">
            <h2 className="italic font-semibold text-base text-grey-750 mb-3">
              Quick Actions
            </h2>
            <button
              type="button"
              onClick={toggleModal}
              className="bg-primary-250 cursor-pointer p-2.5 h-10 w-10 rounded-[64px] ">
              <SearchStatus size="20" color="#82A7E2" variant="Bulk" />
            </button>
          </div>
          <div className="flex justify-center   md:justify-between">
            <div className="flex flex-col md:flex-row  items-center gap-2">
              <button className="flex items-center gap-2 bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                <Graph size="20" color="#82A7E2" variant="Bulk" />
                <span className="text-sm font-semibold text-dark-blue-300 italic">
                  Plan a Budget
                </span>
              </button>
              <button className="flex items-center gap-2 bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                <Gift size="20" color="#FF6630" variant="Bulk" />
                <span className="text-sm font-semibold text-dark-blue-300 italic">
                  Create Gift Registry{' '}
                </span>
              </button>

              <button
                onClick={() => setIsGuestListModalOpen(true)}
                className="flex items-center  cursor-pointer gap-2 bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                <UserAdd size="20" color="#4D55F2" variant="Bulk" />
                <span className="text-sm font-semibold text-dark-blue-300 italic">
                  Create Guestlist
                </span>
              </button>
            </div>
            <button
              type="button"
              onClick={toggleModal}
              className="hidden md:block bg-primary-250 cursor-pointer p-2.5 h-10 w-10 rounded-[64px] ">
              <SearchStatus size="20" color="#82A7E2" variant="Bulk" />
            </button>
          </div>
        </div>
      </div> */}

      {/* Delete Event Confirmation Modal */}
      <DeleteEventConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        eventName={selectedEvent?.title || 'Event'}
        isLoading={deleteEventMutation.isPending}
      />
    </div>
  );
};
