/* eslint-disable @typescript-eslint/no-explicit-any */
import { Link, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "../../components/button/button";
import { Icon } from "../../components/icons/icon";
import { PasswordInput } from "../../components/inputs/password-input/password-input";
import { TextInput } from "../../components/inputs/text-input/text-input";
import { AuthLayout } from "../../layouts/auth-layout";
import logo from "../../assets/icons/Ep-Logo.svg";
import { ArrowRight3 } from "iconsax-react";
import { useForm } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../lib/store/auth";
import { useEventStore } from "../../lib/store/event";
import { PageTitle } from "../../components/helmet/helmet";
import { getOrCreateKeyPair } from "../../lib/utils/crypto";
import { events } from "../../lib/services/events";

type FormData = {
  email: string;
  password: string;
};
export function LoginPage() {
  const navigate = useNavigate();
  const setAuthData = useUserAuthStore((state) => state.setAuthData);
  const setToolStatus = useUserAuthStore((state) => state.setToolStatus);
  const setUserEvents = useEventStore((state) => state.setUserEvents);
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isDirty, touchedFields },
  } = useForm<FormData>({ mode: "onChange" });
  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.login({
        email: data.email,
        password: data.password,
      }),
    onSuccess: async (data) => {
      const access_token = data?.data?.access_token;
      const refresh_token = data?.data?.refresh_token;
      const access_token_expires_at = data?.data?.access_token_expires_at;
      const refresh_token_expires_at = data?.data?.refresh_token_expires_at;
      const userData = data?.data?.user_data;

      setAuthData(
        access_token,
        {
          email: userData?.email,
          first_name: userData?.first_name,
          last_name: userData?.last_name,
          id: userData?.id,
          password_set: userData?.password_set,
          profile_picture: userData?.profile_picture,
          transaction_pin_set: userData?.transaction_pin_set,
        },
        refresh_token,
        access_token_expires_at,
        refresh_token_expires_at
      );

      try {
        // Fetch user events and tool status in parallel
        const [eventsResponse, toolStatusResponse] = await Promise.all([
          events.getEventForAuthUsers(),
          AuthServices.getToolStatus(),
        ]);

        const userEvents = eventsResponse?.data?.events || [];
        const eventsMeta = eventsResponse?.data?.meta || null;
        const toolStatus = toolStatusResponse?.data || null;

        setUserEvents(userEvents, eventsMeta);
        if (toolStatus) {
          setToolStatus(toolStatus);
        }

        if (userEvents.length === 0) {
          navigate('/create-event');
        } else {
          navigate("/select-event");
        }
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        navigate('/create-event');
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const googleMutation = useMutation({
    mutationFn: (publicKey: string) =>
      AuthServices.initiateGoogleOAuth(publicKey),
    onSuccess: (data) => {
      window.location.href = data.data.consent_url;
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const googleOAuthMutation = async () => {
    const { publicKey } = await getOrCreateKeyPair();
    googleMutation.mutate(publicKey);
  };
  const onSubmit = handleSubmit((data) => {
    mutation.mutate(data);
  });

  return (
    <AuthLayout reverse={true} patternFirst={true}>
      <PageTitle title="Login" description="Sign in to your account" />

      <div className="md:p-16 p-8">
        <a
          onClick={(e) => {
            e.preventDefault();
            window.open("https://eventpark.africa/", "_blank");
          }}
          className="flex items-center gap-1.5 mb-[3vh] cursor-pointer"
        >
          <img src={logo} alt="logo" />{" "}
        </a>
        <div className="mb-[4vh]">
          <h1 className="font-semibold text-[32px]">Sign In</h1>
          <h3 className="text-grey-100 mb-8">Welcome Back</h3>
          <form onSubmit={onSubmit}>
            <TextInput
              label="Email"
              id="signin-email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: "Invalid email address",
                },
              })}
              placeholder="<EMAIL>"
              error={touchedFields.email ? errors.email?.message : undefined}
            />
            <div className="mt-5">
              <PasswordInput
                placeholder="Enter your password"
                label="Password"
                id="signin-password"
                {...register("password", {
                  required: "Password is required",
                  minLength: {
                    value: 8,
                    message: "Password must be at least 8 characters",
                  },
                  // pattern: {
                  //   value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/,
                  //   message:
                  //     'Password must contain uppercase, lowercase, and number',
                  // },
                })}
                error={
                  touchedFields.email ? errors.password?.message : undefined
                }
              />
            </div>
            <div className="text-right text-sm italic font-semibold text-primary mt-2 w-full">
              <Link to="/forgot-password">Forgot Password?</Link>
            </div>
            <Button
              type="submit"
              variant="primary"
              className="mt-7"
              isLoading={mutation.isPending}
              disabled={!isValid || !isDirty || mutation.isPending}
            >
              Sign in
            </Button>
          </form>
          <div className="relative w-full py-7">
            <hr className="border border-grey-400" />
            <p className="font-medium text-sm tracking-[0.12em] text-grey-100 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white">
              OR
            </p>
          </div>
          <Button
            type="button"
            variant="secondary"
            className="text-grey-500 gap-3 mb-4"
            onClick={(e) => {
              e.preventDefault();
              googleOAuthMutation();
            }}
            disabled={googleMutation.isPending}
          >
            <Icon name="google" />
            <span>
              {googleMutation.isPending
                ? "Inititatiing..."
                : "Sign in with Google"}
            </span>
          </Button>
          {/* <Button variant="secondary" className="text-grey-500 gap-3">
            <Icon name="facebook" />
            <span>Sign in facebook</span>
          </Button> */}
        </div>
        <div className="flex items-center gap-2">
          <p className="text-grey-500 md:text-lg">No account? </p>
          <Link
            to={"/signup"}
            className="text-primary font-extrabold flex items-center md:text-lg"
          >
            <em>Create Account</em>
            <ArrowRight3 size="24" color="#4D55F2" variant="Bulk" />
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
