/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle } from 'iconsax-react';
import { useMutation } from '@tanstack/react-query';
import { GuestList } from '../../lib/services/guest-list';
import { useEventStore } from '../../lib/store/event';
import { useEventManagement } from '../../lib/hooks/useEventManagement';
import { events } from '../../lib/services/events';
import { toast } from 'react-toastify';

interface LayerData {
  [key: string]: {
    name: string;
    type: 'text' | 'image' | 'solid' | 'bar_code' | 'qr_code';
    placeholder: string;
  };
}

interface TemplateItem {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
  layer_data?: LayerData;
}

interface InvitationFormData {
  [key: string]: string;
}

interface InvitationCardDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTemplate: TemplateItem | null;
  onContinue: (formData: InvitationFormData) => void;
}

export const InvitationCardDetailsModal: React.FC<
  InvitationCardDetailsModalProps
> = ({ isOpen, onClose, selectedTemplate, onContinue }) => {
  const [formData, setFormData] = useState<InvitationFormData>({});
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();

  const updateIVMutation = useMutation({
    mutationFn: (payload: {
      iv_template_id: string;
      iv_template_modifications: { [key: string]: string };
    }) => {
      if (!selectedEvent?.id) {
        throw new Error('No event selected');
      }
      return GuestList.updateEventIVDetails(selectedEvent.id, payload);
    },
    onSuccess: async () => {
      // Fetch the updated event data to get the latest iv_preview_url
      if (selectedEvent?.id) {
        try {
          const updatedEventResponse = await events.getEventByID(
            selectedEvent.id
          );
          const completeEventData = updatedEventResponse.data;

          // Transform CompleteEventData to CreatedEventData format for the store
          const updatedEvent = {
            id: completeEventData.id,
            title: completeEventData.title,
            category_id: completeEventData.category_id,
            category_name: completeEventData.category_name,
            date_from: completeEventData.date_from,
            date_to: completeEventData.date_to,
            description: completeEventData.description,
            location_address: completeEventData.location_address,
            gift_registry_title: completeEventData.gift_registry_title || '',
            delivery_address: completeEventData.delivery_address || '',
            banner_image_id: completeEventData.banner_image_id,
            banner_preview_url: completeEventData.banner_preview_url,
            visibility: completeEventData.visibility,
            invite_link: completeEventData.invite_link,
            gift_registry_link: completeEventData.gift_registry_link,
            iv_preview_url: completeEventData.iv_preview_url,
            status: completeEventData.event_status,
            user_id: completeEventData.user_id,
            created_at: completeEventData.created_at,
            updated_at: completeEventData.updated_at,
          };

          // Update the selectedEvent with the latest data
          updateEventOptimistically(updatedEvent);
        } catch (error) {
          console.error('Failed to refresh event data:', error);
        }
      }

      // toast.success('Invitation details updated successfully!');
      onContinue(formData);
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to update invitation details';
      toast.error(errorMessage);
    },
  });

  useEffect(() => {
    if (isOpen && selectedTemplate?.layer_data) {
      const initialFormData: InvitationFormData = {};

      Object.keys(selectedTemplate.layer_data).forEach((layerKey) => {
        const layer = selectedTemplate.layer_data![layerKey];
        initialFormData[layerKey] =
          layer.type === 'solid' ? layer.placeholder || '#000000' : '';
      });

      setFormData(initialFormData);
    }
  }, [isOpen, selectedTemplate]);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleContinue = () => {
    if (!selectedTemplate?.id) {
      toast.error('No template selected');
      return;
    }

    if (!selectedEvent?.id) {
      toast.error('No event selected');
      return;
    }

    const payload = {
      iv_template_id: selectedTemplate.id,
      iv_template_modifications: formData,
    };

    updateIVMutation.mutate(payload);
  };

  const renderFormField = (layerKey: string, layer: LayerData[string]) => {
    const value = formData[layerKey];

    switch (layer.type) {
      case 'text':
        return (
          <div key={layerKey}>
            <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
              {layer.name}
            </label>
            <input
              type="text"
              value={typeof value === 'string' ? value : ''}
              onChange={(e) => handleInputChange(layerKey, e.target.value)}
              placeholder={layer.placeholder}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>
        );

      case 'image':
        return (
          <div key={layerKey}>
            <label className="block text-sm font-medium text-gray-700 mb-2  capitalize">
              {layer.name}
            </label>
            <input
              type="text"
              value={typeof value === 'string' ? value : ''}
              onChange={(e) => handleInputChange(layerKey, e.target.value)}
              placeholder={layer.placeholder}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>
        );

      case 'solid':
        return (
          <div key={layerKey}>
            <label className="block text-sm font-medium text-gray-700 mb-2  capitalize">
              {layer.name}
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={
                  typeof value === 'string' && value
                    ? value
                    : layer.placeholder || '#000000'
                }
                onChange={(e) => handleInputChange(layerKey, e.target.value)}
                className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={
                  typeof value === 'string' && value
                    ? value
                    : layer.placeholder || '#000000'
                }
                readOnly
                placeholder={layer.placeholder}
                className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base bg-gray-50 cursor-not-allowed"
              />
            </div>
          </div>
        );

      case 'bar_code':
      case 'qr_code':
        return (
          <div key={layerKey}>
            <label className="block text-sm font-medium text-gray-700 mb-2  capitalize">
              {layer.name}
            </label>
            <input
              type="text"
              value={typeof value === 'string' ? value : ''}
              onChange={(e) => handleInputChange(layerKey, e.target.value)}
              placeholder={layer.placeholder}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>
        );

      default:
        return null;
    }
  };

  const isFormValid = () => {
    if (!selectedTemplate?.layer_data) return false;

    return Object.keys(selectedTemplate.layer_data).every((layerKey) => {
      const layer = selectedTemplate.layer_data![layerKey];
      const value = formData[layerKey];

      if (layer.type === 'solid') {
        return typeof value === 'string' && value.length > 0;
      } else {
        return typeof value === 'string' && value.trim().length > 0;
      }
    });
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
        onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="mx-4 w-full max-w-[1056px] max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:w-0 relative rounded-2xl bg-white shadow-xl sm:mx-0"
          onClick={(e) => e.stopPropagation()}>
          <button
            onClick={onClose}
            className="absolute right-4 top-4 z-10 rounded-full p-1 hover:bg-gray-100 transition-colors">
            <CloseCircle size={32} variant="Bulk" color="#4D55F2" />
          </button>
          <div className="flex  overflow-hidden rounded-2xl">
            <div className="w-1/2  p-8 flex items-center justify-center">
              {selectedTemplate?.preview_url ? (
                <div className="w-full max-w-[446px]  bg-gradient-to-br from-purple-50 to-blue-50 p-6 aspect-[3/4] overflow-hidden rounded-[16px]">
                  <img
                    src={selectedTemplate.preview_url}
                    alt={selectedTemplate.name}
                    className="w-full h-full object-cover rounded-xl border-4 border-white -rotate-2"
                  />
                </div>
              ) : (
                <div className="w-full max-w-[280px] aspect-[3/4] rounded-xl bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Template Preview</span>
                </div>
              )}
            </div>

            <div className="w-1/2 py-8 pr-8 overflow-y-auto">
              <div className="mb-6">
                <h2 className="text-2xl md:text-[32px] font-semibold text-gray-900">
                  Invitation Card{' '}
                  <span className="text-[#999999]">Details</span>
                </h2>
                <p className="text-gray-600">
                  Please enter the details you want displayed on your invite{' '}
                </p>
              </div>

              <div className="space-y-5">
                {selectedTemplate?.layer_data &&
                  Object.entries(selectedTemplate.layer_data).map(
                    ([layerKey, layer]) => renderFormField(layerKey, layer)
                  )}
              </div>
              <div className="mt-8 flex justify-end">
                <button
                  onClick={handleContinue}
                  disabled={!isFormValid() || updateIVMutation.isPending}
                  className={`px-8 py-3 rounded-full font-semibold text-white transition-all duration-200 flex items-center gap-2 ${
                    isFormValid() && !updateIVMutation.isPending
                      ? 'bg-primary-650 hover:bg-primary-650 cursor-pointer'
                      : 'bg-primary-650/50 cursor-not-allowed'
                  }`}>
                  {updateIVMutation.isPending ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                      Updating...
                    </>
                  ) : (
                    'Continue'
                  )}
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
