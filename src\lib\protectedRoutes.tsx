import { Navigate, Outlet, useNavigate } from "react-router-dom";
import { ReactNode, useEffect, useState } from "react";
import { isTokenValid } from "./helpers";
import { useUserAuthStore, AuthState } from "./store/auth";
import { useEventStore } from "./store/event";
import moment from "moment";

import { AuthServices } from "./services/auth";

function fetchUserAuthState(): AuthState | null {
  const userAuthStore = localStorage.getItem("user-auth-store");
  if (userAuthStore === null) return null;

  const parsedAuthStore = JSON.parse(userAuthStore) as {
    state: AuthState | undefined;
  };
  if (parsedAuthStore.state == undefined) return null;
  return parsedAuthStore.state;
}

interface AuthStateValidationResult {
  isValid: boolean;
  accessTokenExpired: boolean;
}

function validateUserAuthState(
  userAuthState: AuthState
): AuthStateValidationResult {
  if (
    userAuthState.userAppToken === null ||
    userAuthState.accessTokenExpiresAt === null ||
    userAuthState.refreshToken === null ||
    userAuthState.refreshTokenExpiresAt === null
  ) {
    return { isValid: false, accessTokenExpired: false };
  } else if (
    moment.utc(userAuthState.refreshTokenExpiresAt).isBefore(moment.utc())
  ) {
    return { isValid: false, accessTokenExpired: true };
  }

  if (moment.utc(userAuthState.accessTokenExpiresAt).isBefore(moment.utc())) {
    return {
      isValid: true,
      accessTokenExpired: true,
    };
  }
  return { isValid: true, accessTokenExpired: false };
}

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const authStore = useUserAuthStore.getState();
  const { clearAllEventData } = useEventStore();
  const navigate = useNavigate();

  useEffect(() => {
    const refreshTheToken = async () => {
      try {
        console.log("Refreshing accesstoken...");
        const response = await AuthServices.refreshToken();
        console.log("✅ Proactive refresh response received");
        const {
          access_token,
          refresh_token,
          access_token_expires_at,
          refresh_token_expires_at,
        } = response.data;

        // Update tokens in store with expiry dates
        authStore.setTokens(
          access_token,
          refresh_token,
          access_token_expires_at,
          refresh_token_expires_at
        );

        // Force a small delay to ensure localStorage is updated
        await new Promise((resolve) => setTimeout(resolve, 10));
      } catch (error) {
        console.log(error);
        clearAuthData();
        clearAllEventData();
        console.log("unable to refresh token");
        return navigate("/login");
      }
    };

    const checkAuth = async () => {
      try {
        const userAuthState = fetchUserAuthState();
        if (userAuthState === null) {
          return navigate("/login");
        }
        const { isValid, accessTokenExpired } =
          validateUserAuthState(userAuthState);

        if (!isValid) {
          console.error("Session expired, please login again");
          clearAuthData();
          clearAllEventData();
          return navigate("/login");
        } else if (accessTokenExpired) {
          console.log("expired2");
          await refreshTheToken();
        }
      } catch (error) {
        console.log(error);
      }
    };

    checkAuth();
  }, [navigate, clearAuthData, clearAllEventData, authStore]);

  return children;
};

export const AuthRoute = () => {
  const { userEvents, selectedEvent, setSelectedEvent } = useEventStore();
  const token = (() => {
    const userAuthStore = localStorage.getItem("user-auth-store");
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  if (token) {
    if (userEvents.length === 1 && !selectedEvent) {
      setSelectedEvent(userEvents[0]);
      return <Navigate to="/" replace />;
    }
    if (userEvents.length > 1 && !selectedEvent) {
      return <Navigate to="/select-event" replace />;
    }
    if (userEvents.length > 0 && selectedEvent) {
      return <Navigate to="/" replace />;
    }
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};

export const PublicRoute = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};
export const OnboardingRoute = ({ children }: { children: ReactNode }) => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents } = useEventStore();
  const { clearAllEventData } = useEventStore();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const token = (() => {
    const userAuthStore = localStorage.getItem("user-auth-store");
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!token || !isTokenValid(token)) {
        clearAuthData();
        clearAllEventData();
        navigate("/login");
        return;
      }
      // if (userEvents.length > 0) {
      //   navigate('/', { replace: true });
      //   return;
      // }

      setIsLoading(false);
    };

    checkUserStatus();
  }, [token, navigate, clearAuthData, clearAllEventData, userEvents]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!token || !isTokenValid(token)) {
    return null;
  }

  return children;
};
