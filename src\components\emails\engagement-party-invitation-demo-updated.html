<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engagement Party Invitation - Updated Demo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Alice:wght@400&family=Water+Brush:wght@400&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .demo-controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-controls h2 {
            margin-top: 0;
            color: #333;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input, .input-group textarea {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .input-group textarea {
            height: 60px;
            resize: vertical;
        }
        
        .checkbox-group {
            margin-bottom: 15px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }
        
        .conditional-inputs {
            margin-left: 20px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .char-count {
            margin-left: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .scale-info {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
        }
        
        iframe {
            width: 100%;
            height: 800px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h2>🎉 Updated Template Demo</h2>
        <p>Test the new parameter structure with conditional celebrant logic!</p>
        
        <div class="input-group">
            <label for="eventHeader">Event Header:</label>
            <textarea id="eventHeader" oninput="updatePreview()">Please Join Us For An
Engagement Party In Honor Of</textarea>
        </div>
        
        <div class="checkbox-group">
            <label>
                <input type="checkbox" id="twoCelebrant" onchange="toggleCelebrantInputs()" checked>
                Two Celebrants (if unchecked, single celebrant)
            </label>
        </div>
        
        <div id="twoCelebrantInputs" class="conditional-inputs">
            <div class="input-group">
                <label for="firstCelebrant">First Celebrant:</label>
                <input type="text" id="firstCelebrant" value="Sofia" oninput="updatePreview()">
                <span class="char-count" id="firstCount">5 chars</span>
            </div>
            
            <div class="input-group">
                <label for="secondCelebrant">Second Celebrant:</label>
                <input type="text" id="secondCelebrant" value="Joseph" oninput="updatePreview()">
                <span class="char-count" id="secondCount">6 chars</span>
            </div>
        </div>
        
        <div id="singleCelebrantInputs" class="conditional-inputs hidden">
            <div class="input-group">
                <label for="celebrantName">Celebrant Name:</label>
                <input type="text" id="celebrantName" value="Sofia Martinez" oninput="updatePreview()">
                <span class="char-count" id="singleCount">13 chars</span>
            </div>
        </div>
        
        <div class="scale-info" id="scaleInfo">
            Total: 15 chars | Scale: 1.0 (no scaling needed)
        </div>
        
        <h3>Quick Tests:</h3>
        <button onclick="testTwoCelebrantShort()">Two Celebrants - Short (15 chars)</button>
        <button onclick="testTwoCelebrantLong()">Two Celebrants - Long (32 chars)</button>
        <button onclick="testSingleCelebrantShort()">Single Celebrant - Short (12 chars)</button>
        <button onclick="testSingleCelebrantLong()">Single Celebrant - Long (28 chars)</button>
    </div>
    
    <iframe id="previewFrame" src="data:text/html,Loading..."></iframe>

    <script>
        function calculateScale(totalLength) {
            if (totalLength <= 20) return 1.0;
            if (totalLength >= 33) return 0.2;
            
            const scale = Math.max(0.2, 1.0 - ((totalLength - 20) * 0.067));
            return Math.round(scale * 100) / 100;
        }
        
        function toggleCelebrantInputs() {
            const twoCelebrant = document.getElementById('twoCelebrant').checked;
            const twoInputs = document.getElementById('twoCelebrantInputs');
            const singleInputs = document.getElementById('singleCelebrantInputs');
            
            if (twoCelebrant) {
                twoInputs.classList.remove('hidden');
                singleInputs.classList.add('hidden');
            } else {
                twoInputs.classList.add('hidden');
                singleInputs.classList.remove('hidden');
            }
            
            updatePreview();
        }
        
        function updatePreview() {
            const eventHeader = document.getElementById('eventHeader').value || '';
            const twoCelebrant = document.getElementById('twoCelebrant').checked;
            
            let celebrantText = '';
            let totalLength = 0;
            
            if (twoCelebrant) {
                const firstCelebrant = document.getElementById('firstCelebrant').value || '';
                const secondCelebrant = document.getElementById('secondCelebrant').value || '';
                
                document.getElementById('firstCount').textContent = firstCelebrant.length + ' chars';
                document.getElementById('secondCount').textContent = secondCelebrant.length + ' chars';
                
                celebrantText = firstCelebrant + ' + ' + secondCelebrant;
                totalLength = celebrantText.length;
            } else {
                const celebrantName = document.getElementById('celebrantName').value || '';
                
                document.getElementById('singleCount').textContent = celebrantName.length + ' chars';
                
                celebrantText = celebrantName;
                totalLength = celebrantText.length;
            }
            
            const scale = calculateScale(totalLength);
            
            const scaleInfo = document.getElementById('scaleInfo');
            scaleInfo.innerHTML = `
                Total: <strong>${totalLength} chars</strong> | 
                Scale: <strong>${scale}</strong> 
                ${scale < 1.0 ? '(scaled down)' : '(no scaling needed)'}
            `;
            
            const html = generateInvitationHTML(eventHeader, twoCelebrant);
            
            const frame = document.getElementById('previewFrame');
            frame.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(html);
        }
        
        function generateInvitationHTML(eventHeader, twoCelebrant) {
            const firstCelebrant = document.getElementById('firstCelebrant').value || '';
            const secondCelebrant = document.getElementById('secondCelebrant').value || '';
            const celebrantName = document.getElementById('celebrantName').value || '';
            
            let celebrantHTML = '';
            if (twoCelebrant) {
                celebrantHTML = `<span>${firstCelebrant}&nbsp;</span> + <span>&nbsp;${secondCelebrant}</span>`;
            } else {
                celebrantHTML = celebrantName;
            }
            
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engagement Party Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Alice:wght@400&family=Water+Brush:wght@400&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Alice', serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .invitation-container {
            position: relative;
            width: 620px;
            height: 874px;
            background: linear-gradient(135deg, #8B4513 0%, #DEB887 50%, #8B4513 100%);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            transform: scale(0.7);
        }
        .text-element { position: absolute; color: #FFFFFF; }
        .invitation-title {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 22px;
            line-height: 1.2em;
            letter-spacing: 2%;
            text-align: center;
            left: 156px;
            top: 360px;
            width: 309px;
            height: 52px;
            white-space: pre-line;
        }
        .couple-names {
            font-family: 'Water Brush', cursive;
            font-weight: 400;
            font-size: 85px;
            line-height: 1.05em;
            letter-spacing: 2%;
            color: #BF9346;
            left: 91px;
            top: 426px;
            width: 439px;
            height: 90px;
            display: flex;
            justify-content: center;
            align-items: center;
            transform-origin: center center;
        }
        /* Dynamic scaling */
        .couple-names[data-length="21"] { transform: scale(0.93); }
        .couple-names[data-length="22"] { transform: scale(0.87); }
        .couple-names[data-length="23"] { transform: scale(0.80); }
        .couple-names[data-length="24"] { transform: scale(0.73); }
        .couple-names[data-length="25"] { transform: scale(0.67); }
        .couple-names[data-length="26"] { transform: scale(0.60); }
        .couple-names[data-length="27"] { transform: scale(0.53); }
        .couple-names[data-length="28"] { transform: scale(0.47); }
        .couple-names[data-length="29"] { transform: scale(0.40); }
        .couple-names[data-length="30"] { transform: scale(0.35); }
        .couple-names[data-length="31"] { transform: scale(0.30); }
        .couple-names[data-length="32"] { transform: scale(0.25); }
        .couple-names[data-length="33"], .couple-names[data-length="34"], .couple-names[data-length="35"], 
        .couple-names[data-length="36"], .couple-names[data-length="37"], .couple-names[data-length="38"], 
        .couple-names[data-length="39"], .couple-names[data-length="40"] { transform: scale(0.20); }
        .event-date {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 29px;
            line-height: 1.14em;
            letter-spacing: 2%;
            left: 178px;
            top: 609px;
            width: 264px;
            height: 33px;
            text-align: center;
        }
        .event-time {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 201px;
            top: 674px;
            width: 225px;
            height: 26px;
        }
        .event-address {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 77px;
            top: 704px;
            width: 467px;
            height: 26px;
        }
        .rsvp-info {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 166px;
            top: 740px;
            width: 288px;
            height: 26px;
        }
        .please-reply {
            font-family: 'Water Brush', cursive;
            font-weight: 400;
            font-size: 29px;
            line-height: 0.9em;
            letter-spacing: 2%;
            text-align: center;
            left: 256px;
            top: 806px;
            width: 110px;
            height: 26px;
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="text-element invitation-title">${eventHeader}</div>
        <div class="text-element couple-names">${celebrantHTML}</div>
        <div class="text-element event-date">AUGUST | 22 | 2029</div>
        <div class="text-element event-time">7-11PM</div>
        <div class="text-element event-address">123 any where st, lagos island in any city</div>
        <div class="text-element rsvp-info">RSVP: MARY (+234) 801 234 567</div>
        <div class="text-element please-reply">please reply</div>
    </div>
    <script>
        function calculateCoupleNamesLength() {
            const coupleNamesElement = document.querySelector('.couple-names');
            if (coupleNamesElement) {
                const textContent = coupleNamesElement.textContent || coupleNamesElement.innerText;
                const cleanText = textContent.replace(/\\s+/g, ' ').trim();
                const totalLength = cleanText.length;
                coupleNamesElement.setAttribute('data-length', totalLength.toString());
            }
        }
        document.addEventListener('DOMContentLoaded', calculateCoupleNamesLength);
        setTimeout(calculateCoupleNamesLength, 100);
    </script>
</body>
</html>`;
        }
        
        function testTwoCelebrantShort() {
            document.getElementById('twoCelebrant').checked = true;
            document.getElementById('firstCelebrant').value = 'Ana';
            document.getElementById('secondCelebrant').value = 'Bob';
            toggleCelebrantInputs();
        }
        
        function testTwoCelebrantLong() {
            document.getElementById('twoCelebrant').checked = true;
            document.getElementById('firstCelebrant').value = 'Anastasia';
            document.getElementById('secondCelebrant').value = 'Bartholomew';
            toggleCelebrantInputs();
        }
        
        function testSingleCelebrantShort() {
            document.getElementById('twoCelebrant').checked = false;
            document.getElementById('celebrantName').value = 'Sofia Martinez';
            toggleCelebrantInputs();
        }
        
        function testSingleCelebrantLong() {
            document.getElementById('twoCelebrant').checked = false;
            document.getElementById('celebrantName').value = 'Alexandrina Constantinescu';
            toggleCelebrantInputs();
        }
        
        // Initialize
        updatePreview();
    </script>
</body>
</html> 