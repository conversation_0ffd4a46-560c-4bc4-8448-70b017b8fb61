import { isAxios<PERSON>rror, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InternalAxiosRequestConfig } from "axios";
import axios from "axios";
import { guestTokenManager } from "../utils/guestTokenManager";
import { toast } from "react-toastify";

// Track if guest token refresh is in progress to avoid multiple simultaneous refresh attempts
let isGuestRefreshing = false;
let guestFailedQueue: Array<{
  resolve: (value: string) => void;
  reject: (error: unknown) => void;
}> = [];

const processGuestQueue = (error: unknown, token: string | null = null) => {
  guestFailedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });

  guestFailedQueue = [];
};

// Create a separate API instance for guest operations with automatic token refresh
const createGuestAPI = () => {
  const baseURL = import.meta.env.VITE_API_BASE_URL;

  const axiosInstance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Request interceptor for adding auth token and handling refresh
  axiosInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      // Check if we should add authorization header
      if (isGuestRefreshing) {
        // If already refreshing, queue this request and wait for new token
        return new Promise((resolve, reject) => {
          guestFailedQueue.push({ resolve, reject });
        })
          .then((token) => {
            config.headers.Authorization = `Bearer ${token}`;
            return config;
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      // Get access token (this will automatically refresh if needed)
      try {
        const accessToken = await guestTokenManager.getGuestAccessToken();
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      } catch (error) {
        console.error("Failed to get guest access token:", error);
        // Continue without token for public endpoints
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for handling 401 errors
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as InternalAxiosRequestConfig & {
        _retry?: boolean;
      };

      // Handle 401 errors (fallback for cases where proactive refresh didn't work)
      if (error.response?.status === 401 && !originalRequest._retry) {
        if (guestTokenManager.hasRefreshToken()) {
          if (isGuestRefreshing) {
            // If already refreshing, queue this request
            return new Promise((resolve, reject) => {
              guestFailedQueue.push({ resolve, reject });
            })
              .then((token) => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return axiosInstance(originalRequest);
              })
              .catch((err) => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          isGuestRefreshing = true;

          try {
            console.log(
              "🔄 Starting fallback guest token refresh after 401..."
            );
            const newAccessToken = await guestTokenManager.forceRefreshToken();

            if (newAccessToken) {
              console.log(
                "✅ Fallback guest refresh completed, retrying original request"
              );

              // Process queued requests with new token
              processGuestQueue(null, newAccessToken);

              // Retry original request with new token
              originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
              return axiosInstance(originalRequest);
            } else {
              throw new Error("Failed to refresh token");
            }
          } catch (refreshError) {
            // Refresh failed, clear auth data
            console.error(
              "❌ Fallback guest token refresh failed:",
              refreshError
            );
            processGuestQueue(refreshError, null);
            guestTokenManager.clearGuestToken();
            toast.error("Guest session expired. Please access again.");
            return Promise.reject(refreshError);
          } finally {
            isGuestRefreshing = false;
          }
        } else {
          // No refresh token available, clear auth
          guestTokenManager.clearGuestToken();
          toast.error("Guest session expired. Please access again.");
        }
      }

      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

// Cash Gift Types
export interface CashGift {
  amount: string;
  created_at: string;
  currency: string;
  description: string;
  event_id: string;
  id: string;
  is_crowd_gift: boolean;
  status: string;
  updated_at: string;
}

// Single Cash Gift with Metrics (for detailed view)
export interface CashGiftWithMetrics {
  amount: string;
  created_at: string;
  currency: string;
  description: string;
  event_id: string;
  id: string;
  is_crowd_gift: boolean;
  metrics: {
    crowd_gift_details: {
      total_contributions: string;
      total_contributors: number;
    };
    is_reserved: boolean;
  };
  status: string;
  updated_at: string;
}

// Item Gift Types
export interface ItemGift {
  created_at: string;
  currency_code: string;
  description: string;
  event_id: string;
  id: string;
  image_preview_key: string;
  image_preview_url: string;
  item_link: string;
  name: string;
  price: string;
  quantity: number;
  status: string;
  updated_at: string;
}

// Gift Reservation Types
export interface GiftReservation {
  amount: string;
  created_at: string;
  expires_at: string;
  gift_id: string;
  gift_type: string;
  gifter_email: string;
  gifter_first_name: string;
  gifter_id: string;
  gifter_last_name: string;
  id: string;
  order_number: string;
  status: string;
  updated_at: string;
  collection_transaction_id: string | null; // Can be null for unpaid reservations
}

// Single Reservation with additional details (for detailed view)
export interface SingleReservation {
  amount: string;
  checkout_link: string;
  collection_transaction_id: string;
  created_at: string;
  event_id: string;
  expires_at: string;
  gift_id: string;
  gift_type: string;
  gifter_email: string;
  gifter_first_name: string;
  gifter_id: string;
  gifter_last_name: string;
  id: string;
  order_number: string;
  payment_channel: string;
  payment_initiated: boolean;
  payment_made: boolean;
  payout_transaction_id: string;
  settlement_transaction_id: string;
  status: string;
  updated_at: string;
}

// Meta information for pagination
export interface GiftMeta {
  from: string;
  next_page: boolean;
  page: number;
  page_count: number;
  per_page: number;
  previous_page: boolean;
  to: string;
  total: number;
}

// API Response Types
export interface CashGiftsResponse {
  gifts: CashGift[];
  meta: GiftMeta;
}

export interface ItemGiftsResponse {
  gifts: ItemGift[];
  meta: GiftMeta;
}

export interface ReservationsResponse {
  reservations: GiftReservation[];
  meta: GiftMeta;
}

// Query Parameters
export interface GiftQueryParams {
  from?: string;
  page?: number;
  per_page?: number;
  status?: string;
  to?: string;
}

export interface ReservationQueryParams extends GiftQueryParams {
  type?: string;
  event_id?: string; // Optional - will be auto-populated from guest session if not provided
}

// Request Bodies
export interface GuestRegistrationRequest {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
}

export interface CashReservationRequest {
  amount: string;
}

export interface OrderRequest {
  order_number: string;
}

export interface AccessInitiateRequest {
  email: string;
}

export interface OTPVerifyRequest {
  email: string;
  otp: string;
}

// Access Response Types
export interface AccessInitiateResponse {
  expires_at: string;
}

export interface OTPVerifyResponse {
  access_token: string;
  access_token_expires_at: string;
  refresh_token: string;
  refresh_token_expires_at: string;
  expires_at: string; // Keep for backward compatibility
}

export interface GuestRegistrationResponse {
  access_token: string;
  access_token_expires_at: string;
  refresh_token: string;
  refresh_token_expires_at: string;
  expires_at: string; // Keep for backward compatibility
}

export interface EventDetailsResponse {
  gift_registry_title: string;
  host_first_name: string;
  host_last_name: string;
  id: string;
  most_wanted_gift_id?: string; // ID of the most wanted gift
  delivery_address?: string; // Optional delivery address
  delivery_address_name?: string; // Optional delivery contact name
}

export interface PaymentInitiateRequest {
  channel: string;
}

export interface PaymentInitiateResponse {
  payment_url: string;
  reservation: GiftReservation;
}

export interface PaymentFeeResponse {
  amount: string;
  fee: string;
}

export const GuestGiftsAPI = {
  /**
   * Soft user registration for guests
   */
  async registerGuest(
    request: GuestRegistrationRequest
  ): Promise<GuestRegistrationResponse> {
    try {
      const response = await createGuestAPI().post<GuestRegistrationResponse>(
        `/v1/guest/register`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to register guest",
      };
    }
  },

  /**
   * Fetch event details for guest interface
   */
  async getEventDetails(eventId: string): Promise<EventDetailsResponse> {
    try {
      const response = await createGuestAPI().get<EventDetailsResponse>(
        `/v1/guest/events/${eventId}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch event details",
      };
    }
  },

  /**
   * Fetch cash gifts for an event as a guest
   */
  async getCashGifts(
    eventId: string,
    params: GiftQueryParams = {}
  ): Promise<CashGiftsResponse> {
    try {
      const response = await createGuestAPI().get<CashGiftsResponse>(
        `/v1/guest/events/${eventId}/gifts/cash`,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch cash gifts",
      };
    }
  },

  /**
   * Fetch a single cash gift with metrics as a guest
   */
  async getCashGift(giftId: string): Promise<CashGiftWithMetrics> {
    try {
      const response = await createGuestAPI().get<CashGiftWithMetrics>(
        `/v1/guest/events/gifts/cash/${giftId}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch cash gift",
      };
    }
  },

  /**
   * Fetch item gifts for an event as a guest
   */
  async getItemGifts(
    eventId: string,
    params: GiftQueryParams = {}
  ): Promise<ItemGiftsResponse> {
    try {
      const response = await createGuestAPI().get<ItemGiftsResponse>(
        `/v1/guest/events/${eventId}/gifts/item`,
        { params }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch item gifts",
      };
    }
  },

  /**
   * Create reservation for a cash gift
   */
  async reserveCashGift(
    giftId: string,
    request: CashReservationRequest
  ): Promise<GiftReservation> {
    try {
      const response = await createGuestAPI().post<GiftReservation>(
        `/v1/guest/gifts/cash/${giftId}/reserve`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to reserve cash gift",
      };
    }
  },

  /**
   * Create reservation for an item gift
   */
  async reserveItemGift(giftId: string): Promise<GiftReservation> {
    try {
      const response = await createGuestAPI().post<GiftReservation>(
        `/v1/guest/gifts/item/${giftId}/reserve`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to reserve item gift",
      };
    }
  },

  /**
   * Fetch reservations for a gifter (automatically includes event_id from guest session)
   */
  async getReservations(
    params: ReservationQueryParams = {}
  ): Promise<ReservationsResponse> {
    try {
      // Get event_id from params or guest session automatically
      const eventId = params.event_id || guestTokenManager.getGuestEventId();
      if (!eventId) {
        throw {
          code: "missing_event_id",
          message: "No event ID found in parameters or guest session",
        };
      }

      // Include event_id in the API call parameters
      const apiParams = {
        ...params,
        event_id: eventId,
      };

      const response = await createGuestAPI().get<ReservationsResponse>(
        `/v1/guest/reservations`,
        { params: apiParams }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch reservations",
      };
    }
  },

  /**
   * Fetch a single reservation by ID
   */
  async getReservation(reservationId: string): Promise<SingleReservation> {
    try {
      const response = await createGuestAPI().get<SingleReservation>(
        `/v1/guest/reservations/${reservationId}`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to fetch reservation",
      };
    }
  },

  /**
   * Cancel a gifter's reservation
   */
  async cancelReservation(reservationId: string): Promise<GiftReservation> {
    try {
      const response = await createGuestAPI().post<GiftReservation>(
        `/v1/guest/reservations/${reservationId}/cancel`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to cancel reservation",
      };
    }
  },

  /**
   * Complete item reservation with order number
   */
  async completeItemReservation(
    reservationId: string,
    orderNumber: string
  ): Promise<GiftReservation> {
    try {
      const response = await createGuestAPI().post<GiftReservation>(
        `/v1/guest/reservations/item/${reservationId}/complete`,
        { order_number: orderNumber }
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to complete item reservation",
      };
    }
  },

  /**
   * Set order for a gifter's item reservation
   */
  async setReservationOrder(
    reservationId: string,
    request: OrderRequest
  ): Promise<GiftReservation> {
    try {
      const response = await createGuestAPI().post<GiftReservation>(
        `/v1/guest/reservations/${reservationId}/order`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to set reservation order",
      };
    }
  },

  /**
   * Initiate gift reservation access
   */
  async initiateAccess(
    request: AccessInitiateRequest
  ): Promise<AccessInitiateResponse> {
    try {
      const response = await createGuestAPI().post<AccessInitiateResponse>(
        `/v1/guest/reservations/access/initiate`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to initiate access",
      };
    }
  },

  /**
   * Verify gift reservation access OTP
   */
  async verifyOTP(request: OTPVerifyRequest): Promise<OTPVerifyResponse> {
    try {
      const response = await createGuestAPI().post<OTPVerifyResponse>(
        `/v1/guest/reservations/access/otp/verify`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to verify OTP",
      };
    }
  },

  /**
   * Calculate payment fee for a cash gift reservation
   */
  async calculatePaymentFee(
    reservationId: string
  ): Promise<PaymentFeeResponse> {
    try {
      const response = await createGuestAPI().get<PaymentFeeResponse>(
        `/v1/guest/reservations/cash/${reservationId}/payment/fee`
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to calculate payment fee",
      };
    }
  },

  /**
   * Initiate payment for a cash gift reservation
   */
  async initiatePayment(
    reservationId: string,
    request: PaymentInitiateRequest
  ): Promise<PaymentInitiateResponse> {
    try {
      const response = await createGuestAPI().post<PaymentInitiateResponse>(
        `/v1/guest/reservations/cash/${reservationId}/payment/initiate`,
        request
      );
      return response.data;
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        throw error.response?.data || error;
      }
      throw {
        code: "unknown_error",
        message: "Failed to initiate payment",
      };
    }
  },
};
