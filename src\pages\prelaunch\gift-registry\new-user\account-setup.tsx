/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowRight,
  Bank,
  CloseCircle,
  Moneys,
  SearchStatus,
} from 'iconsax-react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { TextInput } from '../../../../components/inputs/text-input/text-input';
import { useQuery, useMutation } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';
import { ChevronDown } from 'lucide-react';

interface AccountDetailsProps {
  onNextStep: (data: { bank: string; accountNumber: string }) => void;
  initialData?: { bank?: string; accountNumber?: string };
}

interface Bank {
  bank_code: string;
  bank_name: string;
}

interface ValidationResult {
  account_name: string;
  account_number: string;
  bank_code: string;
}
type StatusMessage = {
  type: 'success' | 'error' | 'info' | 'validating' | null;
  content: string;
};

export const AccountSetup = ({
  onNextStep,
  initialData = {},
}: AccountDetailsProps) => {
  const [bank, setBank] = useState(initialData.bank || '');
  const [accountNumber, setAccountNumber] = useState(
    initialData.accountNumber || ''
  );
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [statusMessage, setStatusMessage] = useState<StatusMessage>({
    type: null,
    content: '',
  });
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const {
    data: banks,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payoutBanks', 'ngn'],
    queryFn: () => GiftRegistryServices.getPayoutBanks('ngn'),
  });
  const validationMutation = useMutation({
    mutationFn: (payload: {
      bank_code: string;
      account_number: string;
      currency_code: string;
    }) => GiftRegistryServices.resolveUserPayoutBank(payload),
    onSuccess: (data) => {
      setValidationResult(data.data);
      setStatusMessage({
        type: 'success',
        content: data.data.account_name,
      });
    },
    onError: () => {
      setValidationResult(null);
      setStatusMessage({
        type: 'error',
        content: 'Invalid account details',
      });
    },
  });

  const createBankMutation = useMutation({
    mutationFn: (payload: {
      bank_code: string;
      account_number: string;
      currency_code: string;
    }) => GiftRegistryServices.createUserPayoutBank(payload),
    onSuccess: () => {
      onNextStep({
        bank,
        accountNumber,
      });
    },
    onError: (error: any) => {
      setValidationResult(null);
      setStatusMessage({
        type: 'error',
        content:
          error?.response?.data?.message ||
          'Failed to save bank details. Please try again.',
      });
    },
  });

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const validateAccount = useCallback(
    (bankCode: string, accNumber: string) => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }

      setValidationResult(null);
      setStatusMessage({
        type: 'validating',
        content: 'Validating account...',
      });
      validationTimeoutRef.current = setTimeout(() => {
        validationMutation.mutate({
          bank_code: bankCode,
          account_number: accNumber,
          currency_code: 'ngn',
        });
      }, 500);
    },
    [validationMutation.mutate]
  );

  useEffect(() => {
    if (bank && accountNumber && accountNumber.length >= 10) {
      validateAccount(bank, accountNumber);
    } else {
      setValidationResult(null);
      setStatusMessage({
        type: null,
        content: '',
      });
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    }

    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [bank, accountNumber, validateAccount]);

  const handleContinue = () => {
    if (validationResult) {
      setStatusMessage({ type: null, content: '' }); 
      createBankMutation.mutate({
        bank_code: bank,
        account_number: accountNumber,
        currency_code: 'ngn',
      });
    }
  };

  const handleSkip = () => {
    onNextStep({
      bank: '',
      accountNumber: '',
    });
  };

  // const renderStatusMessage = () => {
  //   // Show validation in progress
  //   if (validationMutation.isPending && bank && accountNumber) {
  //     return (
  //       <span className="text-xs italic text-grin-100">
  //         Validating account...
  //       </span>
  //     );
  //   }

  //   // Show validation success
  //   if (validationResult) {
  //     return (
  // <span className="text-sm flex items-center w-fit gap-2 rounded-full mt-2 font-bold italic text-grin-100 bg-grin py-1 px-2.5">
  //   <svg
  //     width="14"
  //     height="14"
  //     viewBox="0 0 14 14"
  //     fill="none"
  //     xmlns="http://www.w3.org/2000/svg">
  //     <rect x="1" y="1" width="12" height="12" rx="6" fill="#3CC35C" />
  //     <rect
  //       x="1"
  //       y="1"
  //       width="12"
  //       height="12"
  //       rx="6"
  //       stroke="#3CC35C"
  //       strokeWidth="2"
  //     />
  //     <path
  //       d="M9.18395 5.36328L6.18395 8.36328L4.82031 6.99964"
  //       stroke="white"
  //       strokeWidth="1.4"
  //       strokeLinecap="round"
  //       strokeLinejoin="round"
  //     />
  //   </svg>
  //   {validationResult.account_name}
  // </span>
  //     );
  //   }

  //   // Show validation error
  //   if (validationError) {
  //     return (
  //       <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4">
  //         <CloseCircle size={14} color="#B20000" variant="Bulk" />
  //         {validationError}
  //       </span>
  //     );
  //   }

  //   // Show save error (appears below other messages)
  //   if (saveError) {
  //     return (
  //       <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4 mt-2">
  //         <CloseCircle size={14} color="#B20000" variant="Bulk" />
  //         {saveError}
  //       </span>
  //     );
  //   }

  //   // Default info message
  //   if (!bank || !accountNumber) {
  //     return (
  //       <span className="text-xs italic text-grey-650">
  //         Enter account number to receive cash gift
  //       </span>
  //     );
  //   }

  //   return null;
  // };

  const filteredBanks =
    banks?.data?.filter((bankItem: Bank) =>
      bankItem.bank_name.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const isFormValid =
    bank &&
    accountNumber &&
    validationResult &&
    !validationMutation.isPending &&
    !createBankMutation.isPending;
  const renderStatusMessage = () => {
    if (!statusMessage.type) return null;

    switch (statusMessage.type) {
      case 'success':
        return (
          <span className="text-sm flex items-center w-fit gap-2 rounded-full mt-2 font-bold italic text-grin-100 bg-grin py-1 px-2.5">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <rect x="1" y="1" width="12" height="12" rx="6" fill="#3CC35C" />
              <rect
                x="1"
                y="1"
                width="12"
                height="12"
                rx="6"
                stroke="#3CC35C"
                strokeWidth="2"
              />
              <path
                d="M9.18395 5.36328L6.18395 8.36328L4.82031 6.99964"
                stroke="white"
                strokeWidth="1.4"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            {statusMessage.content}
          </span>
        );
      case 'error':
        return (
          <span className="text-sm font-medium italic text-cus-red-100 rounded-full w-fit bg-cus-pink-250 flex items-center gap-2 py-2 px-4">
            <CloseCircle size={14} color="#B20000" variant="Bulk" />
            {statusMessage.content}
          </span>
        );
      case 'validating':
        if (validationMutation.isPending) {
          return (
            <span className="text-xs italic text-grin-100">
              {statusMessage.content}
            </span>
          );
        }
        return null;
      case 'info':
        return (
          <span className="text-xs italic text-grey-650">
            {statusMessage.content}
          </span>
        );
      default:
        return null;
    }
  };
  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <div className="flex justify-between items-center">
          <h2 className="md:text-[40px] text-2xl font-medium -tracking-[0.03em]">
            Add Account details
          </h2>
          <button
            onClick={handleSkip}
            type="button"
            className="border border-cus-orange-150 cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold">
            <span>Skip</span>
            <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
              <ArrowRight color="#FF6630" size="10" />
            </div>
          </button>
        </div>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              type="file"
              accept="image/*"
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />
            <Moneys variant="Bulk" size="62" color="#FFAA8C" />
          </div>
          <div className="-mt-12">
            <div className="bg-cus-pink flex items-center gap-3 p-3.5 mb-6 rounded-2xl">
              <Bank color="#F7BFA9" size={56} variant="Bulk" />
              <p className="italic text-dark-blue-500 font-medium text-xs md:text-sm">
                Add your bank details so guests can easily contribute cash if
                <br /> they prefer to gift money instead of purchasing an item.
              </p>
            </div>
            <div className="mb-6 relative" ref={dropdownRef}>
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Bank
              </label>
              <div
                className={`w-full px-4 py-2.5 bg-white rounded-full text-base border border-grey-200 font-bold text-grey-50 italic outline-0 flex items-center justify-between cursor-pointer transition-all duration-200  ${
                  isLoading ? 'cursor-not-allowed opacity-75' : ''
                }`}
                onClick={() => {
                  if (!isLoading) {
                    setIsOpen(!isOpen);
                    if (!isOpen) {
                      setSearchQuery('');
                    }
                  }
                }}>
                {bank ? (
                  <span className="truncate text-grey-50 not-italic">
                    {banks?.data?.find((b: any) => b.bank_code === bank)
                      ?.bank_name || 'Select a bank'}
                  </span>
                ) : (
                  <span className="text-grey-300">
                    {isLoading ? 'Loading banks...' : 'Select a bank'}
                  </span>
                )}
                <ChevronDown
                  size={20}
                  color="#717680"
                  className={`transition-transform duration-200 ${
                    isOpen ? 'rotate-180' : ''
                  }`}
                />
              </div>
              {isOpen && (
                <div className="absolute z-50 w-full mt-2 shadow-[0px_12px_120px_0px_#5F5F5F0F]  overflow-hidden animate-in fade-in-0 zoom-in-95 duration-200">
                  {error ? (
                    <div className="px-4 py-4 text-sm text-red-600 bg-red-50 border-b border-red-100 flex items-center gap-2">
                      Failed to load banks. Please refresh your page.
                    </div>
                  ) : (
                    <div className="">
                      <div className="sticky top-0 z-10">
                        <div className="relative">
                          <SearchStatus
                            size={16}
                            color="#4D55F2"
                            variant="Bulk"
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10"
                          />
                          <input
                            type="text"
                            placeholder="Search banks..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full pl-10 pr-4 py-2.5 text-sm border rounded-full outline-none  transition-all duration-200 bg-gray-50 focus:bg-white"
                            autoFocus
                          />
                        </div>
                      </div>

                      {/* Banks list */}
                      <div className="max-h-52 overflow-y-auto  bg-white rounded-[14px] [&::-webkit-scrollbar]:hidden rounded-">
                        {filteredBanks.length > 0 ? (
                          <div className="">
                            {filteredBanks.map((bankItem: any) => (
                              <div
                                key={bankItem.bank_code}
                                onClick={() => {
                                  setBank(bankItem.bank_code);
                                  setIsOpen(false);
                                  setSearchQuery('');
                                }}
                                className={`px-4 py-3 text-sm border-y border-[#F0F0F0] cursor-pointer transition-all duration-150 hover:bg-gray-50/20 flex items-center justify-between group ${
                                  bank === bankItem.bank_code
                                    ? ' font-medium text-[#4D55F2]'
                                    : 'text-[#414651]'
                                }`}>
                                <span className="truncate flex-1">
                                  {bankItem.bank_name}
                                </span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="px-4 py-8 text-center">
                            <p className="text-sm text-grey-500">
                              {searchQuery
                                ? 'No banks found matching your search'
                                : 'No banks available'}
                            </p>
                            {searchQuery && (
                              <button
                                onClick={() => setSearchQuery('')}
                                className="text-primary-500 text-sm mt-2 hover:underline">
                                Clear search
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              {error && !isOpen && (
                <div className="flex items-center gap-2 text-xs text-red-500 mt-2">
                  Failed to load banks. Please try again.
                </div>
              )}
            </div>
            <div className="mb-6">
              <TextInput
                id="accountNumber"
                label="Account Number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                placeholder="e.g **********"
                className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
              />
              <div className="mt-3">{renderStatusMessage()}</div>
            </div>
            <button
              onClick={handleContinue}
              disabled={!isFormValid}
              className={`bg-primary-650 text-white py-2.5 px-4 mb-0 rounded-full cursor-pointer flex items-center gap-2 ${
                isFormValid ? '' : 'opacity-50 cursor-not-allowed'
              }`}>
              {isLoading ? 'Loading...' : 'Continue'}
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
