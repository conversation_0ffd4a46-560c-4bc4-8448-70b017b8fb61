import React, { useState, useRef, useEffect } from 'react';
import { Location } from 'iconsax-react';
import { useAddressAutocomplete } from '../../../lib/hooks/useAddressAutocomplete';
import { AddressAutocompleteResponse } from '../../../lib/services/events';

interface AddressAutocompleteProps {
  value: string;
  onChange: (value: string, placeId?: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({
  value,
  onChange,
  placeholder = 'Type your location',
  className = '',
  disabled = false,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { suggestions, isLoading, error, searchAddress, clearSuggestions } =
    useAddressAutocomplete(1000);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);

    if (newValue.trim()) {
      searchAddress(newValue);
      setIsDropdownOpen(true);
    } else {
      clearSuggestions();
      setIsDropdownOpen(false);
    }
  };

  const handleSuggestionClick = (suggestion: AddressAutocompleteResponse) => {
    setInputValue(suggestion.match);
    onChange(suggestion.match, suggestion.place_id);
    setIsDropdownOpen(false);
    clearSuggestions();
  };

  const handleInputFocus = () => {
    if (suggestions.length > 0) {
      setIsDropdownOpen(true);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div className="relative">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          disabled={disabled}
          className={`w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none font-medium text-base ${
            disabled ? 'bg-gray-100 cursor-not-allowed' : ''
          } ${className}`}
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          <Location variant="Bulk" size="20" color="#292D32" />
        </div>

        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-primary-650 rounded-full" />
          </div>
        )}
      </div>

      {isDropdownOpen && (suggestions.length > 0 || error) && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 px-4 bg-white  rounded-lg shadow-[0px_12px_120px_0px_#5F5F5F0F]
 max-h-60 overflow-y-auto">
          {error ? (
            <div className="px-4 py-3 text-sm text-red-600 border-b border-grey-100">
              {error}
            </div>
          ) : (
            suggestions.map((suggestion, index) => (
              <div
                key={`${suggestion.place_id}-${index}`}
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-4 py-3 text-sm cursor-pointer border-b border-grey-150 last:border-b-0 transition-colors duration-150"
                style={{
                  color: index === 0 ? 'var(--color-primary-650)' : '#6B7280',
                  fontWeight: index === 0 ? 500 : 400,
                }}>
                <div className="flex items-center">
                  <Location
                    variant="Bulk"
                    size="16"
                    color={index === 0 ? '#5F66F3' : '#6B7280'}
                    className="mr-3 flex-shrink-0"
                  />
                  <span className="truncate">{suggestion.match}</span>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};
