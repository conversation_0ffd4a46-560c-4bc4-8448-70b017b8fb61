{"name": "event<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-query": "^5.72.1", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "iconsax-react": "^0.0.8", "jwt-decode": "^4.0.0", "lucide-react": "^0.514.0", "moment": "^2.30.1", "motion": "^12.6.5", "papaparse": "^5.5.3", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-otp-input": "^3.1.1", "react-router-dom": "^7.4.0", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.15", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}