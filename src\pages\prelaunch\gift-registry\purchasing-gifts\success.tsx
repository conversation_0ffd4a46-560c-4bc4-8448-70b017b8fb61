import { useNavigate, useLocation, useParams } from "react-router-dom";
import successGIF from "../../../../assets/animations/gift.gif";
import {
  EventDetailsResponse,
  GiftReservation,
  GuestGiftsAPI,
} from "../../../../lib/apis/guestGiftsApi";
import { useState, useEffect } from "react";

export const SuccessPayment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { eventId } = useParams();
  const [eventDetails, setEventDetails] = useState<EventDetailsResponse | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  // Get reservation data from navigation state
  const reservation = location.state?.reservation as
    | GiftReservation
    | undefined;
  const reservationData = location.state?.reservationData;
  const fromPayment = location.state?.fromPayment as boolean | undefined;

  useEffect(() => {
    const fetchEventDetails = async () => {
      try {
        setLoading(true);
        if (!eventId) return;

        const eventResponse = await GuestGiftsAPI.getEventDetails(eventId);
        setEventDetails(eventResponse);
      } catch (error) {
        console.error("Error fetching event details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEventDetails();
  }, [eventId]);
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pt-4 md:pt-0 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center relative">
        {/* Confetti Background Pattern */}
        <img
          src={successGIF}
          alt="gif"
          className="w-full transition-all h-[469px] opacity-40 absolute object-cover top-0 left-0 right-0"
        />

        <div className="relative w-full max-w-[450px] mx-auto">
          <div className="relative z-20 bg-white rounded-[20px] text-center shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            {/* Gift Illustration Background */}
            <div className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[274px] w-full flex items-center justify-center relative overflow-hidden">
              {/* Gift Box SVG */}
              <div className="relative">
                <img src="/success-svg.svg" alt="" />
              </div>
            </div>

            <div className="flex flex-col items-center text-center py-8 px-6 w-full">
              <h2 className="text-[28px] font-medium mb-2 text-[#000073] font-['Rethink_Sans'] tracking-[-0.035em]">
                {fromPayment ? "Payment Successful!" : "You Just gifted"}
              </h2>
              <p className="text-[16px] text-[#808080] mb-6 font-['Rethink_Sans'] tracking-[-0.03em] leading-[1.6]">
                {fromPayment
                  ? "Your gift has been confirmed"
                  : loading
                  ? "Loading..."
                  : `${eventDetails?.host_first_name || "Host"} successfully`}
              </p>

              <p className="text-[#808080] text-base mb-8 font-['Rethink_Sans']">
                {reservation ? (
                  <>
                    You just gifted{" "}
                    <span className="text-[#4D55F2] font-semibold">
                      {reservation.gift_type === "cash"
                        ? `₦${parseFloat(
                            reservation.amount
                          ).toLocaleString()} Cash Gift`
                        : "Gift Item"}
                    </span>
                  </>
                ) : reservationData ? (
                  <>
                    You successfully completed your gift reservation with order
                    number{" "}
                    <span className="text-[#4D55F2] font-semibold">
                      {reservationData.orderNumber}
                    </span>
                  </>
                ) : (
                  <>
                    You just gifted{" "}
                    {loading
                      ? "..."
                      : `${eventDetails?.host_first_name || "the host"}`}{" "}
                    a{" "}
                    {/* <span className="text-[#4D55F2] font-semibold">
                      Iphone 15 Pro
                    </span> */}
                  </>
                )}
              </p>

              <button
                onClick={() => {
                  navigate(`/guest/events/${eventId}/gifts`);
                }}
                type="button"
                className="bg-[#343CD8] cursor-pointer text-base w-full max-w-[306px] text-white py-3 px-6 font-semibold rounded-full hover:bg-[#343CD8]/90 transition-colors shadow-[0px_1px_2px_0px_rgba(10,13,18,0.05)]"
              >
                <span>
                  {fromPayment ? "Back to Gifts" : "Back to Dashboard"}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
