import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Header } from "../../../components/dashboard/header";
import { Footer } from "../footer";
import { useEffect } from "react";
import { isTokenValid } from "../../../lib/helpers";
import { useUserAuthStore } from "../../../lib/store/auth";
import { useEventStore } from "../../../lib/store/event";
import { useToolStatusRefresh } from "../../../lib/hooks/useToolStatusRefresh";

export const PrelaunchDashboard = () => {
  const { clearAuthData } = useUserAuthStore();
  const { userEvents, selectedEvent, setSelectedEvent, clearAllEventData } =
    useEventStore();
  const { refreshToolStatus } = useToolStatusRefresh();

  const location = useLocation();

  const token = (() => {
    const userAuthStore = localStorage.getItem("user-auth-store");
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (userEvents.length === 0) {
      navigate("/create-event");
      return;
    }
    if (userEvents.length === 1 && !selectedEvent) {
      setSelectedEvent(userEvents[0]);
      // Refresh tool status when event is selected
      refreshToolStatus();
      return;
    }
    if (userEvents.length > 1 && !selectedEvent) {
      navigate("/select-event");
      return;
    }

    // Refresh tool status when dashboard loads with valid session and selected event
    if (token && selectedEvent) {
      refreshToolStatus();
    }
  }, [
    token,
    navigate,
    clearAuthData,
    clearAllEventData,
    userEvents,
    selectedEvent,
    setSelectedEvent,
    refreshToolStatus,
  ]);
  if (
    !token ||
    (token && !isTokenValid(token)) ||
    userEvents.length === 0 ||
    !selectedEvent
  ) {
    return null;
  }

  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div
        className={` mx-auto ${
          location.pathname.includes("wallet") ? "w-full" : "max-w-[561px]"
        }`}
      >
        <Header />
        <Outlet />
      </div>
      <Footer />
    </div>
  );
};
