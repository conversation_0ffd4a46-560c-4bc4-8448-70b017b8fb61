import { ArrowCircleRight2, TickCircle } from "iconsax-react";
import { useState } from "react";
import { Button } from "../../../../components/button/onboardingButton";
import { useParams, useNavigate } from "react-router-dom";
import { GuestGiftsAPI } from "../../../../lib/apis/guestGiftsApi";
import { guestTokenManager } from "../../../../lib/utils/guestTokenManager";
import { toast } from "react-toastify";

export const PurchaseTrue = () => {
  const { eventId, giftId } = useParams();
  const navigate = useNavigate();
  const [orderNumber, setOrderNumber] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitOrderNumber = async () => {
    if (!orderNumber.trim()) {
      toast.error("Please enter an order number");
      return;
    }

    setIsSubmitting(true);
    try {
      const accessToken = await guestTokenManager.getGuestAccessToken();
      if (!accessToken) {
        toast.error("Access token not found");
        return;
      }

      // Get the reservation ID from localStorage or find it via API
      const reservationId = guestTokenManager.getGuestReservationId();
      if (!reservationId) {
        toast.error("Reservation not found");
        return;
      }

      // Complete the item reservation with order number
      await GuestGiftsAPI.completeItemReservation(
        reservationId,
        orderNumber.trim()
      );

      toast.success("Order number added successfully!");

      // Navigate to success page instead of showing modal
      navigate(`/guest/events/${eventId}/gifts/item/${giftId}/success`, {
        state: {
          reservationData: {
            orderNumber: orderNumber.trim(),
            reservationId,
          },
        },
      });
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ??
            "Failed to add order number"
          : "Failed to add order number";
      toast.error(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDidntPurchase = () => {
    // Show NotPurchased component instead of going directly back
    // This will be handled by showing NotPurchased in the parent component
    // For now, we'll set a state or navigate to a route that shows NotPurchased
    navigate(
      `/guest/events/${eventId}/gifts/item/${giftId}/jumia?showNotPurchased=true`
    );
  };
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div
          className="bg-white max-w-[450px] rounded-2xl mt-20 p-4 md:p-9 w-full shadow-[0px_12px_120px_0px_#5F5F5F0F]
 "
        >
          <div className="flex justify-center mb-6">
            <div className="p-5 bg-green-100 rounded-full flex items-center justify-center">
              <TickCircle size={67} color="#22C55E" variant="Bold" />
            </div>
          </div>

          <div className="text-center">
            <h2 className="text-4xl font-bold text-dark-blue">Awesome!</h2>
            <p className="text-sm  text-gray-600 font-medium">
              Please add purchase details
            </p>
          </div>
          {/* Description text */}
          <div className="mt-12 mb-6">
            <p className="text-xs text-gray-500  leading-relaxed">
              Help Chinenye keep track of this gift by providing the order
              number below
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-grey-500 mb-2">
              Order Number
            </label>
            <input
              type="text"
              value={orderNumber}
              onChange={(e) => setOrderNumber(e.target.value)}
              className="w-full h-[44px] px-4 outline-none border border-grey-200 rounded-full "
            />
            <p className="text-xs text-grey-950 mt-3">
              Not sure where to find it? Please check your{" "}
              <span className="font-medium">
                confirmation email from Jumia.com
              </span>
            </p>
          </div>

          <Button
            variant="primary"
            size="md"
            onClick={handleSubmitOrderNumber}
            disabled={isSubmitting || !orderNumber.trim()}
            className={`text-white w-full mt-8 mb-4.5 bg-primary-650 disabled:opacity-50 disabled:cursor-not-allowed`}
            iconRight={
              isSubmitting ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
              )
            }
          >
            {isSubmitting ? "Adding..." : "Add Order Number"}
          </Button>

          <button
            onClick={handleDidntPurchase}
            className="text-cus-orange-100 mx-auto w-full underline text-sm font-medium"
          >
            Didn't Purchase this Gift?
          </button>
        </div>
      </div>
    </div>
  );
};
