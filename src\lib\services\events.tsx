import { EventParkAPI } from '../event-park-api';

export interface EventCategories {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEventPayload {
  category_id: string;
  date_from: string;
  date_to: string;
  location_place_id: string;
  title: string;
  description: string;
}

export interface AddressAutocompleteResponse {
  match: string;
  place_id: string;
}

export interface AddressAutocompleteRequest {
  address: string;
}

export interface GuestData {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
}

export interface CreateGuestListPayload {
  id: string; // Event ID
  guests: GuestData[];
  invite_type: string;
}

export interface CreateGuestFilePayload {
  id: string; // Event ID
  file: File;
  invite_type: string;
}

export interface UpdateEventPayload {
  banner_image_id?: string;
  category_id?: string;
  date_from?: string;
  date_to?: string;
  description?: string;
  gift_registry_title?: string;
  location_place_id?: string;
  delivery_address_id?: string;
  most_wanted_gift_id?: string;
  title?: string;
  visibility?: string;
}

export interface GuestParams {
  page?: number;
  per_page?: number;
  from?: string;
  to?: string;
  status?: string;
  search?: string;
}

export const events = {
  getEventCategories: async () => {
    return await EventParkAPI().get('/v1/events/categories');
  },
  createEvent: async (eventData: CreateEventPayload) => {
    return await EventParkAPI().post('/v1/user/events', eventData);
  },
  getEventForAuthUsers: async (params?: {
    page?: number;
    per_page?: number;
    from?: string;
    to?: string;
    status?: string;
  }) => {
    return await EventParkAPI().get('/v1/user/events', { params });
  },
  getEventByID: async (id: string) => {
    return await EventParkAPI().get(`/v1/user/events/${id}`);
  },
  createGuestList: async (payload: CreateGuestListPayload) => {
    return await EventParkAPI().post(
      `/v1/user/events/${payload.id}/guests`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  },
  createGuestListFromFile: async (payload: CreateGuestFilePayload) => {
    const formData = new FormData();
    formData.append('id', payload.id);
    formData.append('file', payload.file);
    formData.append('invite_type', payload.invite_type);

    return await EventParkAPI().post(
      `/v1/user/events/${payload.id}/guests`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },
  getGuestsForAnAuthUser: async (id: string, params?: GuestParams) => {
    return await EventParkAPI().get(`/v1/user/events/${id}/guests`, { params });
  },
  activateGuestList: async (eventId: string) => {
    return await EventParkAPI().post(
      `/v1/user/events/${eventId}/guestlist/activate`
    );
  },
  autocompleteAnAddress: async (
    address: string
  ): Promise<{ data: AddressAutocompleteResponse[] }> => {
    const payload: AddressAutocompleteRequest = { address };
    return await EventParkAPI().post(
      `/v1/user/addresses/autocomplete`,
      payload
    );
  },
  updateEventDetails: async (id: string, payload: UpdateEventPayload) => {
    return await EventParkAPI().patch(`/v1/user/events/${id}`, payload);
  },
  deleteEvent: async (id: string) => {
    return await EventParkAPI().delete(`/v1/user/events/${id}`);
  },
  deleteEventImage: async (id: string) => {
    return await EventParkAPI().delete(`/v1/user/events/images/${id}`);
  },
};
