import { motion, AnimatePresence } from 'framer-motion';
import warn from '../../assets/images/warn.png';
interface DeleteEventConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  eventName: string;
  isLoading?: boolean;
}

export const DeleteEventConfirmationModal: React.FC<
  DeleteEventConfirmationModalProps
> = ({ isOpen, onClose, onConfirm, eventName, isLoading = false }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-white rounded-[16px] shadow-xl max-w-[428px] max-h-[90vh] w-full mx-4 overflow-y-scroll [&::-webkit-scrollbar]:hidden">
            <div className="bg-[linear-gradient(180deg,_#FFE5E5_0%,_#F5F6FE_100%)] flex justify-center py-7">
              <img
                src={warn}
                alt="warning-img"
                className="w-[160px] h-[160px]"
              />
            </div>
            <div className="md:px-10 px-5 pb-8 text-center mt-5">
              <h2 className="text-2xl md:text-[36px] font-semibold text-[#1A22BF]">
                You are about to
              </h2>
              <h3 className="text-2xl md:text-[32px] font-medium text-[#808080] mb-6">
                Delete this Event
              </h3>

              <p className="text-[#808080] text-base leading-relaxed mb-8">
                You are about to delete{' '}
                <span className="font-medium italic text-gray-700">
                  {eventName}
                </span>
                , please note that this event will no longer exist and so all
                data and information about this event would been lost
              </p>
              <div className="flex gap-2 md:gap-4">
                <button
                  onClick={onClose}
                  disabled={isLoading}
                  className="w-full h-[48px] border-1 border-[#4D55F2] text-[#4D55F2] rounded-full font-semibold text-base transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                  Cancel
                </button>

                <button
                  onClick={onConfirm}
                  disabled={isLoading}
                  className="w-full h-[48px]  bg-[#FDEDE6] text-[#00000D] rounded-full font-semibold text-base transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
                  {isLoading ? (
                    <div className="animate-spin h-5 w-5 border-2 border-gray-700 border-t-transparent rounded-full" />
                  ) : (
                    'Delete Event'
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
