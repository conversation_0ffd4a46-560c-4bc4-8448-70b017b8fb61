import { CloseCircle, Tag2 } from "iconsax-react";
import { useState, useEffect } from "react";
import { PurchaseTrue } from "./purchase-true";
import { NotPurchased } from "./not-purchased";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { GuestGiftsAPI, ItemGift } from "../../../../lib/apis/guestGiftsApi";
import { toast } from "react-toastify";

export const BackFromJumia = () => {
  const { eventId, giftId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [purchased, setPurchased] = useState<boolean | null>(null);
  const [itemGift, setItemGift] = useState<ItemGift | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGiftData();

    // Check if we should show NotPurchased directly
    const showNotPurchased = searchParams.get("showNotPurchased");
    if (showNotPurchased === "true") {
      setPurchased(false);
    }
  }, [eventId, giftId, searchParams]);

  const fetchGiftData = async () => {
    try {
      setLoading(true);
      if (!eventId || !giftId) {
        toast.error("Missing event or gift information");
        navigate(-1);
        return;
      }

      // Fetch item gifts to find the specific gift
      const itemGiftsResponse = await GuestGiftsAPI.getItemGifts(eventId);
      const gift = itemGiftsResponse.gifts.find((g) => g.id === giftId);

      if (!gift) {
        toast.error("Gift not found");
        navigate(-1);
        return;
      }

      setItemGift(gift);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load gift details");
      navigate(-1);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="bg-white mt-16 rounded-2xl  w-full max-w-md relative ">
          <button
            className="absolute top-4 right-4 w-8 h-8  flex items-center justify-center"
            onClick={() => navigate(`/guest/events/${eventId}/gifts`)}
          >
            <CloseCircle size="33" color="#634C42" variant="Bulk" />
          </button>

          {loading ? (
            <div className="flex items-center justify-center h-[300px]">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-650"></div>
            </div>
          ) : itemGift ? (
            <>
              <img
                src={itemGift.image_preview_url || "/placeholder-gift.png"}
                alt={itemGift.name}
                className="w-full h-[300px] rounded-t-2xl object-cover"
              />
              <div className="text-center mb-6 mt-5">
                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {itemGift.name}
                </h2>
                <p className="text-sm text-gray-600 mb-3">
                  {itemGift.description}
                </p>
                <div className="mt-3  flex mx-auto items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                  <Tag2 size={12} variant="Bulk" color="#5856D6 " />
                  <span className="text-perple-50 text-sm font-bold">
                    ₦{parseFloat(itemGift.price).toLocaleString()}
                  </span>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-[300px]">
              <p className="text-gray-600">Gift not found</p>
            </div>
          )}

          <div className="text-center mb-6">
            <p className="text-gray-800 font-medium">
              Have you purchased this gift item?
            </p>
          </div>

          <div className="flex gap-4 justify-center mb-10">
            <button
              onClick={() => setPurchased(true)}
              className={`px-6 py-2 rounded-full font-medium transition-all bg-primary text-white`}
            >
              Yes, I have
            </button>
            <button
              onClick={() => setPurchased(false)}
              className={`px-6 py-2 rounded-full font-medium transition-all bg-cus-pink-500`}
            >
              No, I haven't
            </button>
          </div>
        </div>
      </div>
      {purchased === true && <PurchaseTrue />}
      {purchased === false && <NotPurchased />}
    </div>
  );
};
