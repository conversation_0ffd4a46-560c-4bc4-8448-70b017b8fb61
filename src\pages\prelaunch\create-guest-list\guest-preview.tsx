import { ArrowCircleLeft2, CloseCircle } from 'iconsax-react';

interface GuestPreviewProps {
  guests: Guest[];
  onRemoveGuest: (id: number) => void;
  onAddMoreGuests: () => void;
}

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export const GuestPreview = ({
  guests,
  onRemoveGuest,
  onAddMoreGuests,
}: GuestPreviewProps) => {
  return (
    <div className="flex-1">
      <div className="border-b border-grey-850 py-4">
        <button
          type="button"
          onClick={onAddMoreGuests}
          className="p-2 bg-primary rounded-full cursor-pointer">
          <ArrowCircleLeft2 size="18" color="#FFFFFF" variant="Bulk" />
        </button>
      </div>
      <div className="space-y-2 mt-6 h-[450px]  [&::-webkit-scrollbar]:hidden overflow-y-auto pb-5">
        {guests.map((guest) => (
          <div
            key={guest.id}
            className="flex items-center justify-between bg-light-blue-50 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="bg-white text-primary-650 w-10 h-10 rounded-full flex items-center justify-center font-medium">
                {guest.firstName.charAt(0)}
                {guest.lastName.charAt(0)}
              </div>
              <div>
                <h3 className="text-dark-blue-100 font-semibold text-sm">
                  {guest.firstName} {guest.lastName}
                </h3>
                <p className="text-grey-650 text-xs">
                  {guest.email} • {guest.phone}
                </p>
              </div>
            </div>
            <button
              className="cursor-pointer"
              onClick={() => onRemoveGuest(guest.id)}>
              <CloseCircle size="20" color="#9499F7" variant="Bulk" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
