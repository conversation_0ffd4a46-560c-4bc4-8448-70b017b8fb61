import React, { useState, useEffect, useCallback } from "react";
import { ArrowLeft, ArrowRight, Eye, EyeSlash } from "iconsax-react";
import {
  WalletAPI,
  Wallet,
  PayoutAccount,
  PayoutBank,
  WithdrawWalletPayload,
} from "../../../../lib/apis/walletapi";
import { useUserAuthStore } from "../../../../lib/store/auth";
import { toast } from "react-toastify";

interface WithdrawalFlowProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  walletId?: string;
  selectedAccount?: PayoutAccount | null;
}

export const WithdrawalFlow: React.FC<WithdrawalFlowProps> = ({
  isOpen,
  onClose,
  amount,
  walletId,
  selectedAccount: propSelectedAccount,
}) => {
  console.log(walletId);
  const { userData } = useUserAuthStore();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [pin, setPin] = useState(["", "", "", ""]);
  const [confirmPin, setConfirmPin] = useState(["", "", "", ""]);
  const [showPin, setShowPin] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<PayoutAccount | null>(
    propSelectedAccount || null
  );
  const [payoutAccounts, setPayoutAccounts] = useState<PayoutAccount[]>([]);
  const [banks, setBanks] = useState<PayoutBank[]>([]);
  const [bankDetails, setBankDetails] = useState({
    bank: "",
    accountNumber: "",
  });
  const [isVerified, setIsVerified] = useState(false);
  const [loading, setLoading] = useState(false);
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [transactionPin, setTransactionPin] = useState(["", "", "", ""]);
  const [withdrawalAmount] = useState(amount.toString());

  const initializeWithdrawal = useCallback(async () => {
    try {
      setLoading(true);

      // Check if user has wallet
      const wallets = await WalletAPI.getUserWallets();
      if (wallets.length === 0) {
        toast.error("You need to create a wallet first");
        onClose();
        return;
      }

      const userWallet = wallets[0]; // Assuming user has one wallet
      setWallet(userWallet);

      // Check if user has password set - if yes, skip PIN creation step
      if (userData?.password_set && userWallet.pin_set) {
        setCurrentStep(2); // Skip to account selection
      } else if (!userWallet.pin_set) {
        setCurrentStep(1); // Start with PIN creation
      }

      // Load payout accounts and banks
      const [accounts, bankList] = await Promise.all([
        WalletAPI.getPayoutAccounts(),
        WalletAPI.getPayoutBanks("ngn"),
      ]);

      setPayoutAccounts(accounts);
      setBanks(bankList);
    } catch (error) {
      console.error("Failed to initialize withdrawal:", error);
      toast.error("Failed to initialize withdrawal");
      onClose();
    } finally {
      setLoading(false);
    }
  }, [userData?.password_set, onClose]);

  // Check if user has password set and wallet exists
  useEffect(() => {
    if (isOpen) {
      initializeWithdrawal();
    }
  }, [isOpen, initializeWithdrawal]);

  if (!isOpen) return null;

  const handlePinChange = (index: number, value: string, isConfirm = false) => {
    if (value.length > 1) return;

    const newPin = isConfirm ? [...confirmPin] : [...pin];
    newPin[index] = value;

    if (isConfirm) {
      setConfirmPin(newPin);
    } else {
      setPin(newPin);
    }

    // Auto focus next input
    if (value && index < 3) {
      const nextInput = document.getElementById(
        `${isConfirm ? "confirm-" : ""}pin-${index + 1}`
      );
      nextInput?.focus();
    }
  };

  const handleTransactionPinChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newPin = [...transactionPin];
    newPin[index] = value;
    setTransactionPin(newPin);

    // Auto focus next input
    if (value && index < 3) {
      const nextInput = document.getElementById(`transaction-pin-${index + 1}`);
      nextInput?.focus();
    }
  };

  const isPinComplete =
    pin.join("").length === 4 &&
    confirmPin.join("").length === 4 &&
    pin.join("") === confirmPin.join("");

  const isTransactionPinComplete = transactionPin.join("").length === 4;

  const handleSetPin = async () => {
    if (!isPinComplete || !wallet) return;

    try {
      setLoading(true);
      await WalletAPI.setWalletPin(wallet.id, { pin: pin.join("") });
      toast.success("Transaction PIN set successfully!");
      setCurrentStep(2);
    } catch (error) {
      console.error("Failed to set PIN:", error);
      toast.error("Failed to set transaction PIN");
    } finally {
      setLoading(false);
    }
  };

  const handleAccountSelection = (account: PayoutAccount) => {
    setSelectedAccount(account);
  };

  const handleAddNewAccount = async () => {
    if (!bankDetails.bank || !bankDetails.accountNumber || !isVerified) return;

    try {
      setLoading(true);
      const selectedBank = banks.find(
        (bank) => bank.bank_name === bankDetails.bank
      );
      if (!selectedBank) {
        toast.error("Please select a valid bank");
        return;
      }

      const newAccount = await WalletAPI.createPayoutAccount({
        account_number: bankDetails.accountNumber,
        bank_code: selectedBank.bank_code,
        currency_code: "ngn",
      });

      setPayoutAccounts((prev) => [...prev, newAccount]);
      setSelectedAccount(newAccount);
      setCurrentStep(3);
      toast.success("Account added successfully!");
    } catch (error) {
      console.error("Failed to add account:", error);
      toast.error("Failed to add account");
    } finally {
      setLoading(false);
    }
  };

  const handleWithdrawal = async () => {
    if (!wallet || !selectedAccount || !isTransactionPinComplete) return;

    try {
      setLoading(true);

      const withdrawalPayload: WithdrawWalletPayload = {
        amount: withdrawalAmount,
        payout_account_id: selectedAccount.id,
        transaction_pin: transactionPin.join(""),
      };

      const result = await WalletAPI.withdrawFromWallet(
        wallet.id,
        withdrawalPayload
      );

      toast.success("Withdrawal initiated successfully!");
      setCurrentStep(4);

      // You can handle the payment_url if needed
      if (result.payment_url) {
        console.log("Payment URL:", result.payment_url);
      }
    } catch (error) {
      console.error("Failed to withdraw:", error);
      toast.error("Failed to process withdrawal");
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 1
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          1
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 1 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Create Transaction Pin
        </span>
      </div>
      <ArrowRight size="16" color="#E5E7EB" className="mx-4" />
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 2
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          2
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 2 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Select Account
        </span>
      </div>
      <ArrowRight size="16" color="#E5E7EB" className="mx-4" />
      <div className="flex items-center">
        <div
          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
            currentStep >= 3
              ? "bg-primary text-white"
              : "bg-gray-300 text-gray-500"
          }`}
        >
          3
        </div>
        <span
          className={`ml-2 text-sm ${
            currentStep >= 3 ? "text-primary font-medium" : "text-gray-400"
          }`}
        >
          Authenticate
        </span>
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="text-center">
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-8">
        Create and confirm a 4-digit transaction pin that you will remember
      </p>

      {/* PIN Input */}
      <div className="mb-6">
        <div className="flex justify-center gap-3 mb-6">
          {pin.map((digit, index) => (
            <input
              key={index}
              id={`pin-${index}`}
              type={showPin ? "text" : "password"}
              value={digit}
              onChange={(e) => handlePinChange(index, e.target.value)}
              className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
              maxLength={1}
            />
          ))}
        </div>

        <p className="text-sm text-gray-600 mb-4">Confirm PIN</p>
        <div className="flex justify-center gap-3 mb-6">
          {confirmPin.map((digit, index) => (
            <input
              key={index}
              id={`confirm-pin-${index}`}
              type={showPin ? "text" : "password"}
              value={digit}
              onChange={(e) => handlePinChange(index, e.target.value, true)}
              className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
              maxLength={1}
            />
          ))}
        </div>

        <button
          onClick={() => setShowPin(!showPin)}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mx-auto mb-8"
        >
          {showPin ? <EyeSlash size="16" /> : <Eye size="16" />}
          <span className="text-sm">{showPin ? "Hide" : "Show"} PIN</span>
        </button>
      </div>

      <button
        onClick={handleSetPin}
        disabled={!isPinComplete || loading}
        className={`w-full py-3 px-4 rounded-full font-medium ${
          isPinComplete && !loading
            ? "bg-primary text-white hover:bg-primary-600"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
      >
        {loading ? "Setting PIN..." : "Set PIN"}
      </button>
    </div>
  );

  const renderStep2 = () => (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-8">
        Move your funds safely to your bank account
      </p>

      <div className="mb-6">
        <p className="text-gray-700 font-medium mb-4">
          Select the account you would want your funds sent into
        </p>

        <div className="space-y-3 mb-6">
          {/* Existing Accounts */}
          {payoutAccounts.map((account) => (
            <div
              key={account.id}
              onClick={() => handleAccountSelection(account)}
              className={`p-4 border-2 rounded-2xl cursor-pointer transition-all ${
                selectedAccount?.id === account.id
                  ? "border-primary bg-primary-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium">
                    {account.account_name.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {account.account_name}
                    </p>
                    <p className="text-sm text-gray-600">
                      {banks.find(
                        (bank) => bank.bank_code === account.bank_code
                      )?.bank_name || account.bank_code}
                    </p>
                    <p className="text-sm text-gray-500">
                      {account.account_number}
                    </p>
                  </div>
                </div>
                {selectedAccount?.id === account.id && (
                  <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Add New Account */}
          <div
            onClick={() => setCurrentStep(3)}
            className="p-4 border-2 border-dashed rounded-2xl cursor-pointer transition-all border-gray-300 hover:border-gray-400"
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xs">
                  +
                </div>
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  Add New Withdrawal Account
                </p>
                <button className="text-sm text-primary bg-primary-100 px-3 py-1 rounded-full mt-1">
                  Add Account
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={() => setCurrentStep(4)}
        disabled={!selectedAccount}
        className={`w-full py-3 px-4 rounded-full font-medium ${
          selectedAccount
            ? "bg-primary text-white hover:bg-primary-600"
            : "bg-gray-300 text-gray-500 cursor-not-allowed"
        }`}
      >
        Continue
      </button>
    </div>
  );

  const renderStep3 = () => (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdraw from Wallet
      </h1>
      <p className="text-gray-600 mb-6">Add bank details</p>

      {/* Alert */}
      <div className="bg-orange-50 border border-orange-200 rounded-2xl p-4 mb-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">!</span>
          </div>
          <p className="text-orange-800 text-sm">
            Add a new bank account to withdraw your funds. Please ensure the
            account name matches the name on your profile.
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">Bank</label>
          <select
            value={bankDetails.bank}
            onChange={(e) => {
              setBankDetails((prev) => ({ ...prev, bank: e.target.value }));
              if (e.target.value && bankDetails.accountNumber) {
                setTimeout(() => setIsVerified(true), 1000);
              }
            }}
            className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
          >
            <option value="">Select Bank</option>
            {banks.map((bank) => (
              <option key={bank.bank_code} value={bank.bank_name}>
                {bank.bank_name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            Account Number
          </label>
          <input
            type="text"
            value={bankDetails.accountNumber}
            onChange={(e) => {
              setBankDetails((prev) => ({
                ...prev,
                accountNumber: e.target.value,
              }));
              if (e.target.value.length === 10 && bankDetails.bank) {
                setTimeout(() => setIsVerified(true), 1000);
              }
            }}
            placeholder="Enter 10-digit account number"
            maxLength={10}
            className="w-full p-3 border border-gray-300 rounded-2xl focus:outline-none focus:border-primary"
          />
          {isVerified && (
            <div className="flex items-center gap-2 mt-2">
              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
              <span className="text-green-600 text-sm font-medium">
                {userData?.first_name?.toUpperCase()}{" "}
                {userData?.last_name?.toUpperCase()}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-3">
        <button
          onClick={() => setCurrentStep(2)}
          className="flex-1 py-3 px-4 rounded-full font-medium border border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={handleAddNewAccount}
          disabled={!isVerified || loading}
          className={`flex-1 py-3 px-4 rounded-full font-medium ${
            isVerified && !loading
              ? "bg-primary text-white hover:bg-primary-600"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          {loading ? "Adding..." : "Add Account"}
        </button>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Confirm Withdrawal
      </h1>
      <p className="text-gray-600 mb-6">
        Enter your transaction PIN to complete the withdrawal
      </p>

      {/* Withdrawal Summary */}
      <div className="bg-gray-50 rounded-2xl p-6 mb-6">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Amount</span>
            <span className="font-semibold">
              ₦{Number(withdrawalAmount).toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Destination</span>
            <span className="font-semibold">
              {
                banks.find(
                  (bank) => bank.bank_code === selectedAccount?.bank_code
                )?.bank_name
              }{" "}
              • {selectedAccount?.account_number}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Account Name</span>
            <span className="font-semibold">
              {selectedAccount?.account_name}
            </span>
          </div>
        </div>
      </div>

      {/* Transaction PIN Input */}
      <div className="mb-6">
        <p className="text-gray-700 font-medium mb-4">Enter Transaction PIN</p>
        <div className="flex justify-center gap-3 mb-6">
          {transactionPin.map((digit, index) => (
            <input
              key={index}
              id={`transaction-pin-${index}`}
              type="password"
              value={digit}
              onChange={(e) =>
                handleTransactionPinChange(index, e.target.value)
              }
              className="w-12 h-12 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
              maxLength={1}
            />
          ))}
        </div>
      </div>

      <div className="flex gap-3">
        <button
          onClick={() => setCurrentStep(2)}
          className="flex-1 py-3 px-4 rounded-full font-medium border border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={handleWithdrawal}
          disabled={!isTransactionPinComplete || loading}
          className={`flex-1 py-3 px-4 rounded-full font-medium ${
            isTransactionPinComplete && !loading
              ? "bg-primary text-white hover:bg-primary-600"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          {loading ? "Processing..." : "Withdraw"}
        </button>
      </div>
    </div>
  );

  const renderStep5 = () => (
    <div className="text-center py-8">
      {/* Success Icon */}
      <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-lg">✓</span>
        </div>
      </div>

      <h1 className="text-2xl font-semibold text-gray-900 mb-2">
        Withdrawal Successful!
      </h1>
      <p className="text-gray-600 mb-8">
        Your withdrawal request has been processed successfully. The funds will
        be transferred to your account within 24 hours.
      </p>

      <div className="bg-gray-50 rounded-2xl p-6 mb-8">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Amount</span>
            <span className="font-semibold">
              ₦{Number(withdrawalAmount).toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Destination</span>
            <span className="font-semibold">
              {
                banks.find(
                  (bank) => bank.bank_code === selectedAccount?.bank_code
                )?.bank_name
              }{" "}
              • {selectedAccount?.account_number}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Account Name</span>
            <span className="font-semibold">
              {selectedAccount?.account_name}
            </span>
          </div>
        </div>
      </div>

      <button
        onClick={onClose}
        className="w-full py-3 px-4 rounded-full font-medium bg-primary text-white hover:bg-primary-600"
      >
        Done
      </button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl w-full max-w-[522px] h max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft size="20" color="#666" />
            </button>
            <span className="text-gray-600">Withdraw from Wallet</span>
            <div className="w-8"></div>
          </div>

          {currentStep < 5 && renderStepIndicator()}

          {loading && currentStep === 1 && (
            <div className="text-center py-8">
              <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
              <p className="text-gray-600">Initializing withdrawal...</p>
            </div>
          )}
          {!loading && currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderStep4()}
          {currentStep === 5 && renderStep5()}
        </div>
      </div>
    </div>
  );
};
