<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engagement Party Invitation - Demo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Alice:wght@400&family=Water+Brush:wght@400&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .demo-controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-controls h2 {
            margin-top: 0;
            color: #333;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .char-count {
            margin-left: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .scale-info {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
        }
        
        iframe {
            width: 100%;
            height: 800px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h2>🎉 Dynamic Scaling Demo</h2>
        <p>Test different name lengths to see the automatic scaling in action!</p>
        
        <div class="input-group">
            <label for="firstName">First Person's Name:</label>
            <input type="text" id="firstName" value="Sofia" oninput="updatePreview()">
            <span class="char-count" id="firstCount">5 chars</span>
        </div>
        
        <div class="input-group">
            <label for="secondName">Second Person's Name:</label>
            <input type="text" id="secondName" value="Joseph" oninput="updatePreview()">
            <span class="char-count" id="secondCount">6 chars</span>
        </div>
        
        <div class="scale-info" id="scaleInfo">
            Total: 15 chars | Scale: 1.0 (no scaling needed)
        </div>
        
        <h3>Quick Tests:</h3>
        <button onclick="testShortNames()">Short Names (15 chars)</button>
        <button onclick="testMediumNames()">Medium Names (23 chars)</button>
        <button onclick="testLongNames()">Long Names (32 chars)</button>
        <button onclick="testVeryLongNames()">Very Long Names (38 chars)</button>
    </div>
    
    <iframe id="previewFrame" src="data:text/html,Loading..."></iframe>

    <script>
        function calculateScale(totalLength) {
            if (totalLength <= 20) return 1.0;
            if (totalLength >= 33) return 0.2;
            
            // Formula: scale = 1.0 - ((char_count - 20) * 0.067)
            const scale = Math.max(0.2, 1.0 - ((totalLength - 20) * 0.067));
            return Math.round(scale * 100) / 100; // Round to 2 decimal places
        }
        
        function updatePreview() {
            const firstName = document.getElementById('firstName').value || '';
            const secondName = document.getElementById('secondName').value || '';
            
            // Update character counts
            document.getElementById('firstCount').textContent = firstName.length + ' chars';
            document.getElementById('secondCount').textContent = secondName.length + ' chars';
            
            // Calculate total (including ' + ' separator)
            const totalText = firstName + ' + ' + secondName;
            const totalLength = totalText.length;
            const scale = calculateScale(totalLength);
            
            // Update scale info
            const scaleInfo = document.getElementById('scaleInfo');
            scaleInfo.innerHTML = `
                Total: <strong>${totalLength} chars</strong> | 
                Scale: <strong>${scale}</strong> 
                ${scale < 1.0 ? '(scaled down)' : '(no scaling needed)'}
            `;
            
            // Generate the HTML with the current values
            const html = generateInvitationHTML(firstName, secondName);
            
            // Update iframe
            const frame = document.getElementById('previewFrame');
            frame.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(html);
        }
        
        function generateInvitationHTML(firstName, secondName) {
            return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Engagement Party Invitation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Alice:wght@400&family=Water+Brush:wght@400&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Alice', serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .invitation-container {
            position: relative;
            width: 620px;
            height: 874px;
            background: linear-gradient(135deg, #8B4513 0%, #DEB887 50%, #8B4513 100%);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            max-width: 100%;
            max-height: 100vh;
            object-fit: contain;
            transform: scale(0.7);
        }
        .text-element { position: absolute; color: #FFFFFF; }
        .invitation-title {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 22px;
            line-height: 1.2em;
            letter-spacing: 2%;
            text-align: center;
            left: 156px;
            top: 360px;
            width: 309px;
            height: 52px;
        }
        .couple-names {
            font-family: 'Water Brush', cursive;
            font-weight: 400;
            font-size: 85px;
            line-height: 1.05em;
            letter-spacing: 2%;
            color: #BF9346;
            left: 91px;
            top: 426px;
            width: 439px;
            height: 90px;
            display: flex;
            justify-content: center;
            align-items: center;
            transform-origin: center center;
        }
        /* Dynamic scaling */
        .couple-names[data-length="21"] { transform: scale(0.93); }
        .couple-names[data-length="22"] { transform: scale(0.87); }
        .couple-names[data-length="23"] { transform: scale(0.80); }
        .couple-names[data-length="24"] { transform: scale(0.73); }
        .couple-names[data-length="25"] { transform: scale(0.67); }
        .couple-names[data-length="26"] { transform: scale(0.60); }
        .couple-names[data-length="27"] { transform: scale(0.53); }
        .couple-names[data-length="28"] { transform: scale(0.47); }
        .couple-names[data-length="29"] { transform: scale(0.40); }
        .couple-names[data-length="30"] { transform: scale(0.35); }
        .couple-names[data-length="31"] { transform: scale(0.30); }
        .couple-names[data-length="32"] { transform: scale(0.25); }
        .couple-names[data-length="33"], .couple-names[data-length="34"], .couple-names[data-length="35"], 
        .couple-names[data-length="36"], .couple-names[data-length="37"], .couple-names[data-length="38"], 
        .couple-names[data-length="39"], .couple-names[data-length="40"] { transform: scale(0.20); }
        .last-name-phillips {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 24px;
            line-height: 1.14em;
            letter-spacing: 2%;
            color: #BF9346;
            left: 125px;
            top: 530px;
            width: 109px;
            height: 28px;
        }
        .last-name-brown {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 24px;
            line-height: 1.14em;
            letter-spacing: 2%;
            color: #BF9346;
            left: 396px;
            top: 530px;
            width: 90px;
            height: 28px;
        }
        .event-date {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 29px;
            line-height: 1.14em;
            letter-spacing: 2%;
            left: 178px;
            top: 609px;
            width: 264px;
            height: 33px;
            text-align: center;
        }
        .event-time {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 201px;
            top: 674px;
            width: 225px;
            height: 26px;
        }
        .event-address {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 77px;
            top: 704px;
            width: 467px;
            height: 26px;
        }
        .rsvp-info {
            font-family: 'Alice', serif;
            font-weight: 400;
            font-size: 21px;
            line-height: 1.27em;
            letter-spacing: 2%;
            text-transform: uppercase;
            text-align: center;
            left: 166px;
            top: 740px;
            width: 288px;
            height: 26px;
        }
        .please-reply {
            font-family: 'Water Brush', cursive;
            font-weight: 400;
            font-size: 29px;
            line-height: 0.9em;
            letter-spacing: 2%;
            text-align: center;
            left: 256px;
            top: 806px;
            width: 110px;
            height: 26px;
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="text-element invitation-title">
            Please Join Us For An<br>
            Engagement Party In Honor Of
        </div>
        <div class="text-element couple-names">
            <span>${firstName}&nbsp;</span> +
            <span>&nbsp;${secondName}</span>
        </div>
        <div class="text-element last-name-phillips">PHILLIPS</div>
        <div class="text-element last-name-brown">BROWN</div>
        <div class="text-element event-date">AUGUST | 22 | 2029</div>
        <div class="text-element event-time">7-11PM</div>
        <div class="text-element event-address">123 any where st, lagos island in any city</div>
        <div class="text-element rsvp-info">RSVP: MARY (+234) 801 234 567</div>
        <div class="text-element please-reply">please reply</div>
    </div>
    <script>
        function calculateCoupleNamesLength() {
            const coupleNamesElement = document.querySelector('.couple-names');
            if (coupleNamesElement) {
                const textContent = coupleNamesElement.textContent || coupleNamesElement.innerText;
                const cleanText = textContent.replace(/\\s+/g, ' ').trim();
                const totalLength = cleanText.length;
                coupleNamesElement.setAttribute('data-length', totalLength.toString());
            }
        }
        document.addEventListener('DOMContentLoaded', calculateCoupleNamesLength);
        setTimeout(calculateCoupleNamesLength, 100);
    </script>
</body>
</html>`;
        }
        
        function testShortNames() {
            document.getElementById('firstName').value = 'Ana';
            document.getElementById('secondName').value = 'Bob';
            updatePreview();
        }
        
        function testMediumNames() {
            document.getElementById('firstName').value = 'Isabella';
            document.getElementById('secondName').value = 'Alexander';
            updatePreview();
        }
        
        function testLongNames() {
            document.getElementById('firstName').value = 'Anastasia';
            document.getElementById('secondName').value = 'Bartholomew';
            updatePreview();
        }
        
        function testVeryLongNames() {
            document.getElementById('firstName').value = 'Alexandrina';
            document.getElementById('secondName').value = 'Maximillian';
            updatePreview();
        }
        
        // Initialize on page load
        updatePreview();
    </script>
</body>
</html> 