/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowRight, TickCircle } from 'iconsax-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';
import { useEventStore } from '../../../../lib/store/event';
import { useToolStatusRefresh } from '../../../../lib/hooks/useToolStatusRefresh';
import { toast } from 'react-toastify';

export const AllSet = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const { refreshToolStatus } = useToolStatusRefresh();

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const activateGiftRegistryMutation = useMutation({
    mutationFn: (eventId: string) =>
      GiftRegistryServices.activateGiftRegistry(eventId),
    onSuccess: () => {
      refreshToolStatus();
      navigate('/create-gift-registry');
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to activate gift registry'
      );

    },
  });

  const handleGetStarted = () => {
    if (!selectedEvent?.id) {
      toast.error('No event selected. Please select an event first.');
      return;
    }

    activateGiftRegistryMutation.mutate(selectedEvent.id);
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="md:text-[40px] text-2xl font-medium">
          You’re All Set!
          <br /> start curating your gifts
        </h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-grin-50 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <TickCircle size="61" variant="Bulk" color="#3CC35C" />{' '}
          </div>

          <div className="-mt-12">
            <p>
              Congratulations, you’re done setting up your gift registry
              account, you can start curating your gifts
            </p>

            <button
              onClick={handleGetStarted}
              disabled={activateGiftRegistryMutation.isPending}
              className={`py-2.5 px-4 mt-32 mb-0 rounded-full flex items-center gap-2 ${
                activateGiftRegistryMutation.isPending
                  ? 'bg-primary-650/50 text-white cursor-not-allowed'
                  : 'bg-primary-650 text-white cursor-pointer'
              }`}>
              {activateGiftRegistryMutation.isPending
                ? 'Activating...'
                : 'Get Started'}
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
