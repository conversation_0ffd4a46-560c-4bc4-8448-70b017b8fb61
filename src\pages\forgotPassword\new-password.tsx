/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { Button } from "../../components/button/button";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../lib/store/auth";

type FormData = {
  password: string;
};
export const NewPasswordStep = ({
  email,
  fourthStep,
}: {
  email: string;
  fourthStep: () => void;
}) => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const setAuthData = useUserAuthStore((state) => state.setAuthData);

  useEffect(() => {
    if (newPassword && confirmPassword) {
      if (newPassword.length < 8) {
        setPasswordError("Password must be at least 8 characters long");
      } else if (newPassword !== confirmPassword) {
        setPasswordError("Passwords do not match");
      } else {
        setPasswordError("");
      }
    }
  }, [newPassword, confirmPassword]);
  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      AuthServices.completePasswordReset({
        email,
        password: data.password,
      }),
    onSuccess: (data) => {
      const access_token = data?.data?.access_token || "";
      const refresh_token = data?.data?.refresh_token;
      setAuthData(
        access_token,
        {
          email,
          first_name: "",
          last_name: "",
          id: "",
          profile_picture: "",
          password_set: false,
          transaction_pin_set: false,
        },
        refresh_token
      );
      fourthStep();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleSubmit = () => {
    if (!passwordError && newPassword === confirmPassword) {
      mutation.mutate({ password: newPassword });
    }
  };

  const isFormValid = newPassword && confirmPassword && !passwordError;

  return (
    <div className="mb-0 px-[24px]">
      {/* Progress indicator */}
      <div className="flex gap-0 mb-6 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-full"></div>
      </div>

      {/* Header */}
      <h3 className="tracking-[0.12em] text-sm text-grey-250 mb-2">
        YOU ARE SO BACK!!
      </h3>
      <p className="font-semibold text-[32px] mb-4 leading-[96%]">
        Secure your Account
      </p>
      <p className="text-grey-250 mb-8 text-base">
        Please enter in a new password. Ensure not <br /> to forget this
        password this time.
      </p>

      <div className="flex flex-col h-[357.21px] grow-[1] justify-between">
        <div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-grey-500 mb-2">
              New Password
            </label>
            <input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter new password"
              className="w-full h-[56px] px-4 border border-stroke-gray-300 rounded-full shadow-xs shadow-[hsla(220,29%,5%,0.05)] focus:outline-none focus:border-[#A6AAF9]"
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-grey-500 mb-2">
              Confirm Password
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Re-enter new password"
              className="w-full h-[56px] px-4 border border-stroke-gray-300 rounded-full shadow-xs shadow-[hsla(220,29%,5%,0.05)] focus:outline-none focus:border-[#A6AAF9]"
            />
          </div>

          {passwordError && (
            <p className="text-red-500 text-sm mb-4">{passwordError}</p>
          )}
        </div>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!isFormValid || mutation.isPending}
          variant="primary"
          isLoading={mutation.isPending}
        >
          Setup Password
        </Button>
      </div>
    </div>
  );
};
