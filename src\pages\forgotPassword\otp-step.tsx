/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect } from "react";
import { toast } from "react-toastify";
import { Button } from "../../components/button/button";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { useUserAuthStore } from "../../lib/store/auth";
import { formatTime } from "../../lib/helpers";

interface OTPStepProps {
  email: string;
  thirdStep: () => void;
}

export const OTPStep = ({ email, thirdStep }: OTPStepProps) => {
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const setAuthData = useUserAuthStore((state) => state.setAuthData);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [canResend, setCanResend] = useState(false);
  const startCountdown = (expiration: string) => {
    const expirationTime = new Date(expiration).getTime();
    const now = new Date().getTime();
    const remainingTime = Math.max(
      0,
      Math.floor((expirationTime - now) / 1000)
    );

    setTimeLeft(remainingTime);
    setCanResend(remainingTime <= 0);

    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  };
  useEffect(() => {
    const expiresAt = localStorage.getItem("reset_expiry");
    if (!expiresAt) return;
    return startCountdown(expiresAt);
  }, []);
  const verifyMutation = useMutation({
    mutationFn: () =>
      AuthServices.passwordResetOTP({
        otp: otp.join(""),
        email: email,
      }),
    onSuccess: (data) => {
      const access_token = data?.data?.access_token;
      const refresh_token = data?.data?.refresh_token;
      setAuthData(
        access_token,
        {
          email,
          first_name: "",
          last_name: "",
          id: "",
          profile_picture: "",
          password_set: false,
          transaction_pin_set: false,
        },
        refresh_token
      );
      thirdStep();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const resendMutation = useMutation({
    mutationFn: () =>
      AuthServices.passwordResetInitiate({
        email: email,
      }),
    onSuccess: (data) => {
      const newExpiresAt = data?.data?.expires_at;
      if (newExpiresAt) {
        startCountdown(newExpiresAt);
        setCanResend(false);
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });

  const handleResendOTP = () => {
    resendMutation.mutate();
  };

  const handleChange = (index: number, value: string) => {
    if (/^\d*$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value !== "" && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && index > 0 && otp[index] === "") {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split("");
      setOtp(digits);

      inputRefs.current[5]?.focus();
    }
  };

  const isOTPComplete = otp.every((digit) => digit !== "");

  const handleContinue = () => {
    if (isOTPComplete) {
      verifyMutation.mutate();
    }
  };

  return (
    <div className="mb-0 px-[24px]">
      <div className="flex gap-0 mb-6 w-[81px] bg-gray-200 rounded-full">
        <div className="h-[8px] bg-primary-750 rounded-full w-[75%]"></div>
        <div className="h-[8px] w-[25%] bg-gray-200 rounded-full"></div>
      </div>

      {/* Header text */}
      <h3 className="tracking-[0.12em] text-sm text-grey-250 mb-2">
        VERIFY YOUR ACCOUNT{" "}
      </h3>
      <p className="font-semibold text-[32px] mb-4 leading-[96%]">
        Kindly verify email <br /> to continue.
      </p>
      <p className="text-grey-250 mb-6 text-base">
        Check your mail for a 6-Digit otp sent to verify your email address to
        continue password recovery
      </p>
      <div className="flex flex-col grow-[1] h-[324.21px] justify-between">
        <div>
          <div className="flex justify-between mb-6">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={(el) => {
                  if (el) inputRefs.current[index] = el;
                }}
                type="text"
                inputMode="numeric"
                placeholder="0"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className={`w-[56px] h-[56px] placeholder:text-grey-200 text-center border border-primary-950 rounded-full text-[48px] font-medium leading-[60px] tracking-[-2%] shadow-xs shadow-[hsla(220,29%,5%,0.05)] ${
                  digit
                    ? "text-[#00000D] border-[#DBDDFC]"
                    : "border-stroke-gray-300"
                } focus:outline-none focus:border-[#A6AAF9] focus:text-[#4D55F2] focus:shadow-[0px_0px_0px_4px_#DBDDFC] `}
              />
            ))}
          </div>
          {/* Resend OTP option */}
          <div className="flex items-center gap-3 border border-grey-800 py-1 pr-1 pl-3.5 text-nowrap rounded-full mb-14 max-w-[218px] text-sm font-medium">
            <span className="text-grey-100 leading-none">No OTP Yet?</span>
            <Button
              variant="neutral"
              onClick={handleResendOTP}
              disabled={!canResend || resendMutation.isPending}
              className={`bg-cus-pink-300 h-7 px-3 text-nowrap  items-center text-primary-650 rounded-full ${
                !canResend && "text-xs !cursor-default font-bold "
              }`}
            >
              {canResend ? "Resend OTP" : `Resend in ${formatTime(timeLeft)}`}
            </Button>
          </div>
        </div>

        {/* Rest of your existing OTP step UI */}
        <Button
          type="submit"
          onClick={handleContinue}
          disabled={!isOTPComplete || verifyMutation.isPending}
          variant="primary"
          isLoading={verifyMutation.isPending}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};
