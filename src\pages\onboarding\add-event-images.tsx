/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowCircleRight2,
  ArrowRight,
  Gallery,
  CloseCircle,
} from 'iconsax-react';
import { But<PERSON> } from '../../components/button/onboardingButton';
import frame from '../../assets/images/bg-frame.png';
import { toast } from 'react-toastify';
import { AuthServices } from '../../lib/services/auth';
import { Onboarding } from '../prelaunch/onboarding';
import { events } from '../../lib/services/events';
import { useEventStore } from '../../lib/store/event';

interface ImageWithPreview {
  id: string;
  file: File;
  preview: string;
  isUploading?: boolean;
  uploadSuccess?: boolean;
  uploadError?: string | null;
}

interface AddEventImagesProps {
  // onNext?: (images: File[]) => void;
  // onSkip?: () => void;
  initialData?: File[];
  direction?: 'forward' | 'backward';
  eventId?: string;
  eventName?: string;
}

export const AddEventImages = ({
  initialData = [],
  direction = 'forward',
  eventId,
  eventName,
}: AddEventImagesProps) => {
  const [bannerImage, setBannerImage] = useState<File | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string | null>(null);
  const [isBannerUploading, setIsBannerUploading] = useState(false);
  const [bannerUploadSuccess, setBannerUploadSuccess] = useState(false);
  const [bannerUploadError, setBannerUploadError] = useState<string | null>(
    null
  );
  const [eventImages, setEventImages] = useState<ImageWithPreview[]>(() => {
    return initialData.map((file, index) => ({
      id: `initial-${index}-${Date.now()}`,
      file,
      preview: URL.createObjectURL(file),
    }));
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  const bannerInputRef = useRef<HTMLInputElement>(null);
  const eventImagesInputRef = useRef<HTMLInputElement>(null);
  const { setCreatedEventData } = useEventStore();

  const MAX_FILE_SIZE_MB = Number(import.meta.env.VITE_MAX_IMAGE_SIZE) || 10;
  const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

  useEffect(() => {
    return () => {
      if (bannerPreview) {
        URL.revokeObjectURL(bannerPreview);
      }
      eventImages.forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
    };
  }, [bannerPreview, eventImages]);

  const handleBannerImageUpload = async (file: File) => {
    if (file && file.type.startsWith('image/')) {
      if (file.size > MAX_FILE_SIZE_BYTES) {
        toast.error(
          `Banner image size must be less than ${MAX_FILE_SIZE_MB}MB`
        );
        return;
      }
      setBannerImage(file);
      const preview = URL.createObjectURL(file);
      setBannerPreview(preview);
      if (eventId) {
        setIsBannerUploading(true);
        setBannerUploadError(null);

        try {
          const response = await AuthServices.uploadFiles(
            file,
            'event_banner',
            eventId
          );
          console.log('Banner upload successful:', response);
          try {
            const updatedEventResponse = await events.getEventByID(eventId);
            if (updatedEventResponse?.data) {
              setCreatedEventData(updatedEventResponse.data);
            }
            setBannerUploadSuccess(true);
          } catch (fetchError) {
            console.error('Failed to fetch updated event data:', fetchError);
            setBannerUploadError('Failed to update event data after upload');
          }
        } catch (error: any) {
          console.error('Banner upload failed:', error);
          let errorMessage =
            error?.response?.data?.message ||
            error?.message ||
            'Failed to upload banner image. Please try again.';

          if (error && typeof error === 'object') {
            if (
              'response' in error &&
              error.response &&
              typeof error.response === 'object' &&
              'data' in error.response &&
              error.response.data &&
              typeof error.response.data === 'object' &&
              'message' in error.response.data
            ) {
              errorMessage = String(error.response.data.message);
            } else if (
              'message' in error &&
              typeof error.message === 'string'
            ) {
              errorMessage = error.message;
            }
          }

          setBannerUploadError(errorMessage);
          toast.error(errorMessage);
        } finally {
          setIsBannerUploading(false);
        }
      } else {
        toast.warning(
          'Event ID is missing. Banner will be uploaded when you click Continue.'
        );
      }
    } else if (file && !file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
    }
  };

  const uploadSingleEventImage = async (imageData: ImageWithPreview) => {
    if (!eventId) {
      return;
    }
    setEventImages((prev) =>
      prev.map((img) =>
        img.id === imageData.id
          ? { ...img, isUploading: true, uploadError: null }
          : img
      )
    );

    try {
      const response = await AuthServices.uploadFiles(
        imageData.file,
        'event_image',
        eventId
      );

      console.log(
        `Event image upload successful for ${imageData.file.name}:`,
        response
      );

      setEventImages((prev) =>
        prev.map((img) =>
          img.id === imageData.id
            ? { ...img, isUploading: false, uploadSuccess: true }
            : img
        )
      );
      try {
        const updatedEventResponse = await events.getEventByID(eventId);
        if (updatedEventResponse?.data) {
          setCreatedEventData(updatedEventResponse.data);
        }
      } catch (fetchError) {
        console.error('Failed to fetch updated event data:', fetchError);
      }
    } catch (error) {
      console.error(
        `Event image upload failed for ${imageData.file.name}:`,
        error
      );
      let errorMessage = 'Upload failed';

      if (error && typeof error === 'object') {
        if (
          'response' in error &&
          error.response &&
          typeof error.response === 'object' &&
          'data' in error.response &&
          error.response.data &&
          typeof error.response.data === 'object' &&
          'message' in error.response.data
        ) {
          errorMessage = String(error.response.data.message);
        } else if ('message' in error && typeof error.message === 'string') {
          errorMessage = error.message;
        }
      }
      setEventImages((prev) =>
        prev.map((img) =>
          img.id === imageData.id
            ? { ...img, isUploading: false, uploadError: errorMessage }
            : img
        )
      );

      toast.error(`Failed to upload ${imageData.file.name}: ${errorMessage}`);
    }
  };

  const handleEventImagesUpload = async (files: FileList) => {
    const newImages: ImageWithPreview[] = [];
    const rejectedFiles: string[] = [];

    Array.from(files).forEach((file) => {
      if (!file.type.startsWith('image/')) {
        rejectedFiles.push(`${file.name} (not an image)`);
        return;
      }

      if (file.size > MAX_FILE_SIZE_BYTES) {
        rejectedFiles.push(
          `${file.name} (exceeds ${MAX_FILE_SIZE_MB}MB limit)`
        );
        return;
      }

      const id = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const preview = URL.createObjectURL(file);
      newImages.push({
        id,
        file,
        preview,
        isUploading: true, // Start with uploading state immediately
        uploadSuccess: false,
        uploadError: null,
      });
    });

    if (rejectedFiles.length > 0) {
      toast.error(`Some files were rejected: ${rejectedFiles.join(', ')}`);
    }

    const totalImages = eventImages.length + newImages.length;
    if (totalImages > 4) {
      const allowedCount = 4 - eventImages.length;
      newImages.slice(allowedCount).forEach((image) => {
        URL.revokeObjectURL(image.preview);
      });
      newImages.splice(allowedCount);
      toast.error('Maximum of 4 event images allowed (plus 1 banner image)');
    }

    if (newImages.length > 0) {
      // Add images to state first with uploading state (skeleton loaders show immediately)
      setEventImages((prev) => [...prev, ...newImages]);

      // Then start uploading all images simultaneously (not sequentially)
      newImages.forEach((imageData) => {
        uploadSingleEventImage(imageData);
      });
    }
  };

  const removeEventImage = (id: string) => {
    setEventImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleEventImagesUpload(files);
    }
  };

  const handleContinue = async () => {
    // Check if there are any images still uploading
    const hasUploadingImages =
      eventImages.some((img) => img.isUploading) || isBannerUploading;

    if (hasUploadingImages) {
      toast.warning(
        'Please wait for all images to finish uploading before continuing.'
      );
      return;
    }
    const failedImages = eventImages.filter((img) => img.uploadError);
    if (failedImages.length > 0) {
      toast.error(
        `${failedImages.length} image(s) failed to upload. Please retry or remove them before continuing.`
      );
      return;
    }

    if (bannerUploadError) {
      toast.error(
        'Banner image upload failed. Please retry before continuing.'
      );
      return;
    }
    setShowOnboarding(true);
  };

  const handleSkip = () => {
    setShowOnboarding(true);
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      opacity: 0,
      x: direction === 'forward' ? 50 : -50,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <motion.h2
          className="text-xl md:text-3xl font-medium text-black"
          initial="hidden"
          animate="visible"
          variants={titleVariants}>
          <AnimatePresence mode="wait">
            <motion.span
              key="upload-photos"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}>
              Upload <span className="text-primary-650 italic">photos</span> of
              your
              <br />
              event 📷
            </motion.span>
          </AnimatePresence>
        </motion.h2>

        <button
          type="button"
          onClick={handleSkip}
          className="border border-cus-orange-150 cursor-pointer text-cus-orange-500 flex items-center gap-2 px-3 py-2 rounded-full text-sm font-semibold disabled:opacity-50 disabled:cursor-not-allowed">
          <span>Skip</span>
          <div className="bg-cus-orange-100/30 h-4 w-4 rounded-full flex justify-center items-center">
            <ArrowRight color="#FF6630" size="10" />
          </div>
        </button>
      </div>

      <motion.div
        className="bg-white rounded-[20px] mt-14 px-5 py-6 w-full"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}>
        <div className="mb-14 ">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              ref={bannerInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleBannerImageUpload(file);
              }}
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
              disabled={isBannerUploading}
            />

            {isBannerUploading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="w-16 h-16 bg-gray-300 rounded-full animate-pulse flex items-center justify-center">
                  <div className="w-8 h-8 bg-gray-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            )}

            {bannerPreview && !isBannerUploading ? (
              <div className="relative w-full h-full">
                <img
                  src={bannerPreview}
                  alt="Banner"
                  className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${
                    bannerUploadSuccess ? 'opacity-100' : 'opacity-70'
                  }`}
                />
                {bannerUploadError && (
                  <div className="absolute top-2 right-2">
                    <button
                      onClick={() =>
                        bannerImage && handleBannerImageUpload(bannerImage)
                      }
                      className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                      title="Retry upload">
                      ↻
                    </button>
                  </div>
                )}
              </div>
            ) : !isBannerUploading ? (
              <Gallery variant="Bulk" size="62" color="#992600" />
            ) : null}
          </div>
          <p
            className={`text-xs italic text-primary-750 -mt-15 font-medium cursor-pointer ${
              isBannerUploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={() =>
              !isBannerUploading && bannerInputRef.current?.click()
            }>
            {isBannerUploading ? 'Uploading banner...' : 'Upload banner image'}
          </p>
          {bannerUploadError && (
            <p className="text-xs text-red-500 -mt-12 font-medium">
              {bannerUploadError}
            </p>
          )}
        </div>
        {eventImages.length > 0 && (
          <div className="mb-6">
            <div className="flex flex-wrap gap-3">
              {eventImages.map((image, index) => (
                <div key={image.id} className="relative">
                  {image.isUploading && (
                    <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg z-10 flex items-center justify-center">
                      <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse"></div>
                    </div>
                  )}

                  <img
                    src={image.preview}
                    alt={`Event ${index + 1}`}
                    className={`w-21 h-25 object-cover rounded-lg border border-grey-200 transition-opacity duration-300 ${
                      image.isUploading
                        ? 'opacity-50'
                        : image.uploadSuccess
                        ? 'opacity-100'
                        : image.uploadError
                        ? 'opacity-70'
                        : 'opacity-100'
                    }`}
                  />
                  {image.uploadError && !image.isUploading && (
                    <div className="absolute top-1 right-1 z-20">
                      <button
                        onClick={() => uploadSingleEventImage(image)}
                        className="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                        title="Retry upload">
                        ↻
                      </button>
                    </div>
                  )}

                  {!image.isUploading && (
                    <button
                      onClick={() => removeEventImage(image.id)}
                      className="absolute -top-2 -right-1 bg-white rounded-full z-20">
                      <CloseCircle size="20" color="#CC0000" variant="Bold" />
                    </button>
                  )}

                  {image.isUploading && (
                    <div className="absolute bottom-1 left-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded text-center z-20">
                      Uploading...
                    </div>
                  )}

                  {image.uploadError && !image.isUploading && (
                    <div className="absolute bottom-1 left-1 right-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded text-center z-20">
                      Failed
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {eventImages.length < 4 && (
          <div className="mb-6">
            <div
              className={`rounded-2xl relative overflow-hidden cursor-pointer transition-all ${
                isDragOver ? 'ring-2 ring-primary-650 ring-opacity-50' : ''
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => eventImagesInputRef.current?.click()}>
              <div className="w-full h-auto">
                <img
                  src={frame}
                  alt="frame"
                  className="w-full h-auto object-contain"
                />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-white px-4 py-2 rounded-full ">
                  <p className="text-primary text-sm font-bold italic">
                    Click to Upload Images of your Events
                  </p>
                </div>
              </div>
              <input
                ref={eventImagesInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => {
                  const files = e.target.files;
                  if (files) handleEventImagesUpload(files);
                }}
                className="hidden"
              />
            </div>
            <div className=" flex justify-between items-center mt-2">
              <p className="text-[#6B7280] text-xs">
                Allowed formats: PNG, JPEG, JPG up to {MAX_FILE_SIZE_MB}MB each
              </p>
              <p className="text-[#343CD8] text-xs italic">
                Maximum of 4 selections
              </p>
            </div>
          </div>
        )}

        <Button
          variant="primary"
          size="md"
          className={`text-white  ${
            eventImages.some((img) => img.uploadSuccess) || bannerUploadSuccess
              ? 'bg-primary-650'
              : '!bg-primary-650/35 mt-6'
          } ${eventImages.length === 4 && 'mt-40'}`}
          onClick={handleContinue}
          disabled={
            eventImages.some((img) => img.isUploading) || isBannerUploading
          }
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }>
          {eventImages.some((img) => img.isUploading)
            ? 'Images uploading...'
            : isBannerUploading
            ? 'Banner uploading...'
            : 'Continue'}
        </Button>
      </motion.div>
      {showOnboarding && <Onboarding eventName={eventName} />}
    </div>
  );
};
