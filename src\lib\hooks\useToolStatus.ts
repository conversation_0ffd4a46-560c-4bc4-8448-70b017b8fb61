import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { AuthServices } from '../services/auth';
import { useUserAuthStore } from '../store/auth';

// Global event emitter for tool status updates
class ToolStatusEventEmitter {
  private listeners: (() => void)[] = [];

  subscribe(listener: () => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  emit() {
    this.listeners.forEach((listener) => listener());
  }
}

export const toolStatusEmitter = new ToolStatusEventEmitter();

/**
 * Custom hook to manage tool status
 * Provides cached tool status and ability to refresh when needed
 */
export const useToolStatus = () => {
  const { toolStatus, setToolStatus } = useUserAuthStore();
  const queryClient = useQueryClient();

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['toolStatus'],
    queryFn: async () => {
      const response = await AuthServices.getToolStatus();
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    enabled: !toolStatus, // Only fetch if we don't have cached data
  });

  // Update store when data changes
  useEffect(() => {
    if (data) {
      setToolStatus(data);
    }
  }, [data, setToolStatus]);

  // Listen for global tool status update events
  useEffect(() => {
    const unsubscribe = toolStatusEmitter.subscribe(() => {
      refetch();
    });
    return unsubscribe;
  }, [refetch]);

  const refreshToolStatus = async () => {
    const { data: freshData } = await refetch();
    if (freshData) {
      setToolStatus(freshData);
    }
    return freshData;
  };

  const invalidateToolStatus = () => {
    queryClient.invalidateQueries({ queryKey: ['toolStatus'] });
  };

  const broadcastToolStatusUpdate = () => {
    toolStatusEmitter.emit();
  };

  return {
    toolStatus: toolStatus || data,
    isLoading,
    error,
    refreshToolStatus,
    invalidateToolStatus,
    broadcastToolStatusUpdate,
  };
};
