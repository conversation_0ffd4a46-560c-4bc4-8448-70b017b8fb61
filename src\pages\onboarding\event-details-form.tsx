/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  useState,
  // ChangeEvent,
  useEffect,
  useRef,
} from 'react';
import { Button } from '../../components/button/onboardingButton';
import {
  ArrowCircleRight2,
  Calendar,
  Clock,
  // Gallery,
  CloseCircle,
} from 'iconsax-react';
import { AddressAutocomplete } from '../../components/inputs/address-autocomplete';
import { motion, AnimatePresence } from 'framer-motion';
import {
  fadeInRightTextVariants,
  fadeInRightLetterVariants,
} from '../../components/reuseables/animations/animations';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';
import { CreateEventPayload } from '../../lib/services/events';
import { CreatedEventData } from '../../lib/store/event';
import { useEventManagement } from '../../lib/hooks/useEventManagement';

interface EventDetailsFormProps {
  onNext: (
    details: {
      name: string;
      startDate: string;
      startTime: string;
      endDate: string;
      endTime: string;
      location: string;
      image?: File;
      imageUrl?: string;
      description: string;
    },
    createdEventData?: CreatedEventData
  ) => void;
  initialData?: {
    name: string;
    description: string;
    startDate?: string;
    startTime?: string;
    endDate?: string;
    endTime?: string;
    location: string;
    imageUrl?: string;
    imageFile?: File;
  };
  direction: 'forward' | 'backward';
  categoryId?: string;
}

export const EventDetailsForm = ({
  onNext,
  initialData,
  direction,
  categoryId,
}: EventDetailsFormProps) => {
  const { createEventMutation } = useEventManagement();
  const [eventTitle, setEventTitle] = useState(initialData?.name || '');
  const [eventDescription, setEventDescription] = useState(
    initialData?.description || ''
  );
  const [eventLocation, setEventLocation] = useState(
    initialData?.location || ''
  );
  const [eventLocationPlaceId, setEventLocationPlaceId] = useState<string>('');
  // const [imageFile, setImageFile] = useState<File | null>(
  //   initialData?.imageFile || null
  // );
  // const [imagePreview, setImagePreview] = useState<string | null>(
  //   initialData?.imageUrl || null
  // );
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: initialData?.startDate ? new Date(initialData.startDate) : undefined,
    to: initialData?.endDate ? new Date(initialData.endDate) : undefined,
  });
  const [dateRangeText, setDateRangeText] = useState(() => {
    if (initialData?.startDate && initialData?.endDate) {
      const start = format(new Date(initialData.startDate), 'MMM dd');
      const end = format(new Date(initialData.endDate), 'MMM dd, yyyy');
      return `${start} - ${end}`;
    }
    return '';
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [eventTime, setEventTime] = useState(() => {
    return initialData?.startTime || '';
  });
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        timePickerRef.current &&
        !timePickerRef.current.contains(event.target as Node)
      ) {
        setShowTimePicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false);
        } else if (showTimePicker) {
          setShowTimePicker(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDatePicker, showTimePicker]);

  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      setShowDatePicker(false);
    }
  };

  const handleDateRangeSelect = (
    range: { from: Date | undefined; to?: Date | undefined } | undefined
  ) => {
    if (range) {
      const normalizedRange = {
        from: range.from,
        to: range.to || undefined,
      };
      setDateRange(normalizedRange);

      if (normalizedRange.from && normalizedRange.to) {
        const startText = format(normalizedRange.from, 'MMM dd');
        const endText = format(normalizedRange.to, 'MMM dd, yyyy');
        setDateRangeText(`${startText} - ${endText}`);
        // Remove the following line to prevent auto-closing
        // setShowDatePicker(false);
      } else if (normalizedRange.from) {
        const startText = format(normalizedRange.from, 'MMM dd, yyyy');
        setDateRangeText(startText);
      } else {
        setDateRangeText('');
      }
    } else {
      setDateRange({ from: undefined, to: undefined });
      setDateRangeText('');
    }
  };
  const handleTimeSelect = (time: string) => {
    setEventTime(time);
    setShowTimePicker(false);
  };

  const validateForm = () => {
    return (
      eventTitle.trim() !== '' &&
      eventDescription.trim() !== '' &&
      dateRange.from !== undefined &&
      dateRange.to !== undefined &&
      eventTime.trim() !== '' &&
      eventLocation.trim() !== '' &&
      eventLocationPlaceId.trim() !== ''
    );
  };
  // const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (file) {
  //     setImageFile(file);
  //     const previewUrl = URL.createObjectURL(file);
  //     setImagePreview(previewUrl);
  //   }
  // };

  // useEffect(() => {
  //   return () => {
  //     if (imagePreview && !initialData?.imageUrl) {
  //       URL.revokeObjectURL(imagePreview);
  //     }
  //   };
  // }, [imagePreview, initialData?.imageUrl]);

  const handleLocationChange = (value: string, placeId?: string) => {
    setEventLocation(value);
    if (placeId) {
      setEventLocationPlaceId(placeId);
    }
  };

  const handleCreateEvent = (eventData: CreateEventPayload) => {
    createEventMutation.mutate(eventData, {
      onSuccess: (data) => {
        const eventDetails = {
          name: eventTitle,
          startDate: dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : '',
          startTime: eventTime,
          endDate: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : '',
          endTime: eventTime,
          location: eventLocation,
          description: eventDescription,
          // image: imageFile || undefined,
          // imageUrl: imagePreview || undefined,
        };

        onNext(eventDetails, data.data);
      },
    });
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const formatDateTimeForAPI = (date: Date, time: string) => {
        const [hours, minutes] = time.split(':').map(Number);
        const newDate = new Date(date);
        newDate.setHours(hours, minutes, 0, 0);
        return newDate.toISOString();
      };

      const eventPayload: CreateEventPayload = {
        category_id: categoryId || '1',
        title: eventTitle,
        description: eventDescription,
        date_from: formatDateTimeForAPI(dateRange.from!, eventTime),
        date_to: formatDateTimeForAPI(dateRange.to!, eventTime),
        location_place_id: eventLocationPlaceId || '',
      };
      handleCreateEvent(eventPayload);
    }
  };

  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      x: direction === 'forward' ? '20vw' : '-20vw',
      rotate: direction === 'forward' ? 4 : -4,
      opacity: 0,
    },
    visible: {
      x: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 20,
        mass: 0.2,
        delay: 0.2,
        velocity: 2,
      },
    },
  });

  const datePickerStyles = {
    button: { color: '#4D55F2' },
    caption: { color: '#4D55F2' },
    day_selected: { backgroundColor: '#4D55F2' },
    day_today: { color: '#4D55F2', fontWeight: 'bold' },
  };

  const disabledDays = { before: new Date() };

  return (
    <div>
      <motion.h2
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={fadeInRightTextVariants}>
        <AnimatePresence mode="wait">
          {["Let's", 'curate', 'your'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}
              className={
                word === 'curate'
                  ? 'bg-gradient-to-b from-[#343CD8] via-[#A6AAF9] to-[#A6AAF9] bg-clip-text text-transparent italic'
                  : ''
              }>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
          <br />
          {['first', 'event', '🎉'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
        </AnimatePresence>
      </motion.h2>
      <motion.div
        className="bg-white rounded-[20px] mt-14 px-5 py-6 w-full"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}>
        {/* Image circle */}
        {/* <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 opacity-0 cursor-pointer z-10"
          />
          {imagePreview ? (
            <img
              src={imagePreview}
              alt="Event"
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <Gallery variant="Bulk" size="62" color="#992600" />
          )}
        </div> */}

        <div className="space-y-4 ">
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event Name
            </label>
            <input
              type="text"
              placeholder="e.g Shola’s Bridal party"
              value={eventTitle}
              onChange={(e) => setEventTitle(e.target.value)}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event Description{' '}
            </label>
            <input
              type="text"
              placeholder="e.g bridesmaid celebration for all ladies"
              value={eventDescription}
              onChange={(e) => setEventDescription(e.target.value)}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
            />
          </div>

          <div className="flex  gap-4">
            <div className="flex-1">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Event Date
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Select Date Range"
                  value={dateRangeText}
                  readOnly
                  onClick={() => setShowDatePicker(true)}
                  className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowDatePicker(true)}>
                  <Calendar variant="Bulk" size="20" color="#292D32" />
                </div>
              </div>
            </div>

            <div className="w-[118px]">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Time of Event
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="00:00"
                  value={eventTime}
                  readOnly
                  onClick={() => setShowTimePicker(true)}
                  className="w-full py-2.5 pl-10  rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowTimePicker(true)}>
                  <Clock variant="Bulk" size="20" color="#292D32" />
                </div>

                {/* Time Picker Dropdown */}
                {showTimePicker && (
                  <div
                    ref={timePickerRef}
                    className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-2 left-0 w-[120px] max-h-[200px] overflow-y-auto"
                    style={{ scrollbarWidth: 'thin' }}>
                    <div className="flex flex-col">
                      {timeOptions.map((time) => (
                        <button
                          key={time}
                          onClick={() => handleTimeSelect(time)}
                          className={`text-left px-3 py-2 hover:bg-primary-250 rounded-md text-sm ${
                            eventTime === time
                              ? 'bg-primary-250 text-primary-650 font-medium'
                              : 'text-grey-500'
                          }`}>
                          {time}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Location
            </label>
            <AddressAutocomplete
              value={eventLocation}
              onChange={handleLocationChange}
              placeholder="Type your location"
              disabled={createEventMutation.isPending}
            />
          </div>

          <Button
            variant="primary"
            size="md"
            className={`text-white mt-14 ${
              validateForm() ? 'bg-primary-650' : 'bg-primary-650/35'
            }`}
            onClick={handleSubmit}
            disabled={!validateForm() || createEventMutation.isPending}
            iconRight={
              createEventMutation.isPending ? (
                <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
              ) : (
                <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
              )
            }>
            {createEventMutation.isPending ? 'Creating...' : 'Continue'}
          </Button>
        </div>
      </motion.div>
      <AnimatePresence>
        {showDatePicker && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/40  backdrop-blur-sm"
            onClick={handleBackdropClick}>
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="mx-4 max-h-[90vh] w-full max-w-[522px]  relative overflow-y-scroll lg:overflow-hidden rounded-2xl bg-white shadow-xl sm:mx-0"
              onClick={(e) => e.stopPropagation()}>
              <div className="text-center flex-1 pr-6 mt-8">
                <h3 className="text-[28px] font-medium text-dark-200">
                  Event Date
                </h3>
                <p className="mt-1 text-base text-grey-250">
                  Please choose the date(s) for your event
                </p>
              </div>
              <button
                onClick={() => setShowDatePicker(false)}
                className="absolute top-4 right-4 transition-colors">
                <CloseCircle size="33" color="#4D55F2" variant="Bulk" />
              </button>
              <div className="p-6 flex justify-center">
                <style>
                  {`                               
              .rdp-caption_label {
                color: #000;
                font-weight: 600;
                text-align: center;
                font-size: 16px;
                font-style: italic;
              }
              .rdp-day_range_middle {
                background-color: rgba(77, 85, 242, 0.1);
              }
              .rdp {
                margin: 0;
                display: flex;
                justify-content: center;
              }
              .rdp-months {
                display: flex;
                justify-content: center;
              }
              .rdp-month {
                margin: 0;
              }
              .rdp-table {
                margin: 0 auto;
              }
              .rdp-caption {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 1rem;
              }
              .rdp-nav {
                display: flex;
                align-items: center;
              }
            `}
                </style>
                <DayPicker
                  mode="range"
                  selected={dateRange}
                  onSelect={handleDateRangeSelect}
                  disabled={disabledDays}
                  styles={datePickerStyles}
                  startMonth={new Date()}
                  className="w-full flex justify-center"
                />
              </div>

              <div className="flex items-center justify-between pb-10 pt-2 px-6">
                {/* <button
                  onClick={() => {
                    setDateRange({
                      from: undefined,
                      to: undefined,
                    });
                    setDateRangeText('');
                  }}
                  className="text-primary-650 hover:text-primary-700 text-sm font-medium transition-colors">
                  Clear Selection
                </button> */}
                <button
                  onClick={() => setShowDatePicker(false)}
                  className="bg-primary-650 hover:bg-primary-650/90 w-full  rounded-full md:mx-20 h-[44px] text-sm font-medium text-white transition-colors">
                  Continue
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
