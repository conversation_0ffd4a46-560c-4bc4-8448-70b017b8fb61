import { useState, useEffect } from 'react';
import { SingleGuestWarningModal } from '../../../components/modals/SingleGuestWarningModal';
import { useGuestList } from '../../../lib/contexts/GuestListContext';

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface SendEmailInviteProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
  onGuestsChange?: (guests: Guest[], source?: 'email') => void;
}

export const SendEmailInvite = ({
  onNextStep,
  onFormActiveChange,
  onGuestsChange,
}: SendEmailInviteProps) => {
  const {
    guests: contextGuests,
    setGuests: setContextGuests,
    guestSource,
  } = useGuestList();
  const [emails, setEmails] = useState<string[]>(
    guestSource === 'email'
      ? contextGuests.map((guest) => guest.email).filter((email) => email)
      : []
  );
  const [inputValue, setInputValue] = useState('');
  const [emailError, setEmailError] = useState('');
  const [showSingleGuestWarningModal, setShowSingleGuestWarningModal] =
    useState(false);

  useEffect(() => {
    onFormActiveChange?.(inputValue !== '' || emails.length > 0);
  }, [inputValue, emails.length, onFormActiveChange]);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      const email = inputValue.trim();
      setEmailError('');

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }

      setEmails([...emails, email]);
      setInputValue('');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (emailError) {
      setEmailError('');
    }
  };

  const handleAddToQueue = () => {
    if (inputValue.trim()) {
      const email = inputValue.trim();
      setEmailError('');

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }

      setEmails([...emails, email]);
      setInputValue('');
    }
  };

  const removeEmail = (index: number) => {
    const updatedEmails = emails.filter((_, i) => i !== index);
    setEmails(updatedEmails);
  };

  const handleInviteGuests = () => {
    const hasQueuedEmails = emails.length > 0;
    const hasInputData = inputValue.trim() !== '';

    // If user has queued emails but no input data, proceed directly
    if (hasQueuedEmails && !hasInputData) {
      // Check if only one email in queue, show warning modal
      if (emails.length === 1) {
        setShowSingleGuestWarningModal(true);
        return;
      }

      // Proceed with existing queue
      const emailGuests: Guest[] = emails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '', // Email invites don't have names
        lastName: '',
        email: email,
        phone: '', // Email invites don't have phone numbers
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');

      if (onNextStep) {
        onNextStep();
      }
      return;
    }

    // If user has input data, validate it
    if (hasInputData) {
      setEmailError('');
      const email = inputValue.trim();

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }

      // If user has queued emails AND valid input data, add input to queue first
      if (hasQueuedEmails) {
        const updatedEmails = [...emails, email];

        // Check if total will be only one email, show warning modal
        if (updatedEmails.length === 1) {
          setShowSingleGuestWarningModal(true);
          return;
        }

        const emailGuests: Guest[] = updatedEmails.map((email, index) => ({
          id: Date.now() + index,
          firstName: '', // Email invites don't have names
          lastName: '',
          email: email,
          phone: '', // Email invites don't have phone numbers
        }));
        setContextGuests(emailGuests, 'email');
        onGuestsChange?.(emailGuests, 'email');
        setInputValue('');
        setEmailError('');

        // Then proceed with the updated queue
        if (onNextStep) {
          onNextStep();
        }
        return;
      }

      // If user has valid input data but no queued emails, show warning modal
      if (!hasQueuedEmails) {
        setShowSingleGuestWarningModal(true);
        return;
      }
    }

    // If no input data and no queued emails, show error
    if (!hasInputData && !hasQueuedEmails) {
      setEmailError('Please add at least one email address');
      return;
    }
  };

  const handleSingleGuestWarningClose = () => {
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningAddToQueue = () => {
    // Add the current input to queue
    if (
      inputValue.trim() &&
      validateEmail(inputValue.trim()) &&
      !emails.includes(inputValue.trim())
    ) {
      handleAddToQueue();
    }
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningContinue = () => {
    setShowSingleGuestWarningModal(false);

    // Determine what to proceed with
    const hasQueuedEmails = emails.length > 0;
    const hasInputData =
      inputValue.trim() !== '' && validateEmail(inputValue.trim());

    if (hasInputData && !hasQueuedEmails) {
      // User has only input data, proceed with that
      const emailGuests: Guest[] = [
        {
          id: Date.now(),
          firstName: '', // Email invites don't have names
          lastName: '',
          email: inputValue.trim(),
          phone: '', // Email invites don't have phone numbers
        },
      ];
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
    } else if (hasQueuedEmails && hasInputData) {
      // User has both queued emails and input data, combine them
      const updatedEmails = [...emails, inputValue.trim()];
      const emailGuests: Guest[] = updatedEmails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '', // Email invites don't have names
        lastName: '',
        email: email,
        phone: '', // Email invites don't have phone numbers
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
      setInputValue('');
      setEmailError('');
    } else if (hasQueuedEmails && !hasInputData) {
      // User has only queued emails, proceed with those
      const emailGuests: Guest[] = emails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '', // Email invites don't have names
        lastName: '',
        email: email,
        phone: '', // Email invites don't have phone numbers
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
    }

    if (onNextStep) {
      onNextStep();
    }
  };

  return (
    <div className="flex-1  pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium ">
        invite guest via email
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-6">
        Got emails for your guests? Add them and send invites
      </p>

      <div className="">
        <div>
          <label className="block text-sm text-grey-500 font-medium mb-1.5">
            Email
          </label>
          <div className="">
            <input
              type="email"
              placeholder="Enter your guest's email"
              className={` pr-3.5 py-2.5 pl-2.5 w-full rounded-full text-base text-grey-300 outline-none border ${
                emailError ? 'border-red-500' : 'border-grey-200'
              }`}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
            />
          </div>
          {emailError && (
            <p className="text-red-500 text-xs mt-1 ml-2">{emailError}</p>
          )}
        </div>
        <button
          onClick={handleAddToQueue}
          disabled={!inputValue.trim()}
          className="bg-primary-250 mt-3 hover:bg-primary-250/50 text-primary  text-sm cursor-pointer font-medium py-1 px-3 rounded-full flex items-center mb-2.5">
          <span className="bg-primary text-white text-xs  rounded-full h-3 w-3 flex items-center justify-center mr-2">
            +
          </span>
          Add to Queue
        </button>
        <div className="flex flex-wrap gap-2 mt-5">
          {emails.map((email, index) => (
            <div
              key={index}
              className="bg-gray-100 px-3 py-1 rounded-full flex items-center gap-2">
              <span className="text-grey-500 text-xs md:text-sm font-medium">
                {email}
              </span>
              <button
                onClick={() => removeEmail(index)}
                className="text-grey-500 cursor-pointer">
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-18 py-3.5 border-t border-grey-850 flex justify-end ">
        <button
          onClick={handleInviteGuests}
          className="text-base font-semibold mr-5 text-white h-12 max-w-[135px] w-full rounded-full transition-colors bg-primary cursor-pointer hover:bg-primary/80">
          Invite Guests
        </button>
      </div>

      {/* Single Guest Warning Modal */}
      <SingleGuestWarningModal
        isOpen={showSingleGuestWarningModal}
        onClose={handleSingleGuestWarningClose}
        onAddToQueue={handleSingleGuestWarningAddToQueue}
        onContinue={handleSingleGuestWarningContinue}
      />
    </div>
  );
};
