/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowCircleRight2, CloseCircle, Tag2, TruckTick } from 'iconsax-react';
// import { Icon } from '../../../components/icons/icon';
import { Success } from './success';
import { useState } from 'react';
import { useGiftItems } from '../../../lib/contexts/GiftItemsContext';
import { GiftRegistryServices } from '../../../lib/services/gift-registry';
import { useEventStore } from '../../../lib/store/event';
import { AuthServices } from '../../../lib/services/auth';
import { events } from '../../../lib/services/events';
import { toast } from 'react-toastify';

interface GiftItem {
  id: number;
  name: string;
  description?: string;
  price?: number | string;
  image: string | File; // Update to allow both string and File types
  quantity?: number;
  item_link?: string;
  mostWanted?: boolean;
}

interface GiftItemResponse {
  id: string;
  name: string;
  description: string;
  price: string;
  quantity: number;
  item_link: string;
  status: string;
  created_at: string;
  updated_at: string;
  event_id: string;
  currency_code: string;
  image_preview_url?: string;
}

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  giftItems?: GiftItem[];
}

interface PreviewAndCreateProps {
  initialData?: RegistryData;
  onClose?: () => void;
}

export const PreviewAndCreate = ({
  initialData = {},
  onClose = () => {},
}: PreviewAndCreateProps) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // const [uploadProgress, setUploadProgress] = useState(0);
  // const [error, setError] = useState<string | null>(null);
  const { giftItems, removeGiftItem, getItemCount, getMostWantedItem } =
    useGiftItems();
  const { selectedEvent } = useEventStore();

  const items = giftItems.length > 0 ? giftItems : initialData.giftItems || [];

  const dataURLtoFile = (dataUrl: string, filename: string): File | null => {
    try {
      if (dataUrl.startsWith('http')) {
        return null;
      }
      if (!dataUrl.startsWith('data:')) {
        return null;
      }
      const arr = dataUrl.split(',');
      if (arr.length < 2) {
        return null;
      }

      const mimeMatch = arr[0].match(/:(.*?);/);
      const mime = mimeMatch?.[1];
      if (!mime) {
        return null;
      }
      if (!mime.startsWith('image/')) {
        return null;
      }

      try {
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        const file = new File([u8arr], filename, { type: mime });
        // Validate file size (e.g., max 10MB)
        const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSizeInBytes) {
          console.error(`File ${filename} is too large: ${file.size} bytes`);
          return null;
        }

        return file;
      } catch (decodeError) {
        console.error(
          `Error decoding base64 data for ${filename}:`,
          decodeError
        );
        return null;
      }
    } catch (error) {
      console.error(
        `Error converting data URL to file for ${filename}:`,
        error
      );
      return null;
    }
  };

  const blobURLtoFile = async (
    blobUrl: string,
    filename: string
  ): Promise<File | null> => {
    try {
      const response = await fetch(blobUrl);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch blob: ${response.status} ${response.statusText}`
        );
      }

      const blob = await response.blob();
      if (!blob.type.startsWith('image/')) {
        return null;
      }
      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
      if (blob.size > maxSizeInBytes) {
        return null;
      }
      const file = new File([blob], filename, {
        type: blob.type || 'image/jpeg',
      });
      return file;
    } catch (error) {
      console.error(`Failed to convert blob URL for ${filename}:`, error);
      return null;
    }
  };

  const uploadGiftItemImages = async (
    createdItems: GiftItemResponse[],
    eventId: string
  ) => {
    const totalItems = createdItems.length;
    // let uploadedCount = 0;
    const uploadErrors: string[] = [];
    const uploadWarnings: string[] = [];

    console.log(`Starting image uploads for ${totalItems} items`, {
      eventId,
      itemIds: createdItems.map((item) => item.id),
    });

    for (let i = 0; i < createdItems.length; i++) {
      const createdItem = createdItems[i];
      const originalItem = items.find((item) => item.name === createdItem.name);
      if (!originalItem?.image) {
        uploadWarnings.push(`No image provided for ${createdItem.name}`);
        // uploadedCount++;
        // setUploadProgress(Math.floor((uploadedCount / totalItems) * 100));
        continue;
      }
      if (
        typeof originalItem.image === 'string' &&
        originalItem.image.startsWith('http')
      ) {
        console.log(
          `Image for ${createdItem.name} is already a URL, skipping upload`
        );
        // uploadedCount++;
        // setUploadProgress(Math.floor((uploadedCount / totalItems) * 100));
        continue;
      }

      let fileToUpload: File | null = null;

      try {
        if (originalItem.image instanceof File) {
          if (!originalItem.image.type.startsWith('image/')) {
            throw new Error(`File is not an image: ${originalItem.image.type}`);
          }
          const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
          if (originalItem.image.size > maxSizeInBytes) {
            throw new Error(
              `File is too large: ${Math.round(
                originalItem.image.size / 1024 / 1024
              )}MB`
            );
          }

          fileToUpload = originalItem.image;
        } else if (originalItem.image.startsWith('blob:')) {
          console.log(`Processing blob URL for ${createdItem.name}`);
          fileToUpload = await blobURLtoFile(
            originalItem.image,
            `gift-item-${createdItem.id}.jpg`
          );
        } else if (originalItem.image.startsWith('data:')) {
          console.log(`Processing data URL for ${createdItem.name}`);
          fileToUpload = dataURLtoFile(
            originalItem.image,
            `gift-item-${createdItem.id}.jpg`
          );
        } else {
          console.warn(`Unknown image format for ${createdItem.name}:`, {
            imageValue: originalItem.image.substring(0, 100),
            type: typeof originalItem.image,
          });
          throw new Error(`Unsupported image format`);
        }

        if (!fileToUpload) {
          throw new Error(`Failed to process image`);
        }
        const uploadResponse = await AuthServices.uploadFiles(
          fileToUpload,
          'item_gift',
          eventId,
          createdItem.id
        );
        console.log(`Upload successful for ${createdItem.name}`, {
          response: uploadResponse.data,
          status: uploadResponse.status || 'unknown',
        });

        // uploadedCount++;
        // setUploadProgress(Math.floor((uploadedCount / totalItems) * 100));
      } catch (err: any) {
        uploadErrors.push(
          `Failed to upload image for ${createdItem.name}: ${
            err.message || 'Unknown error'
          }`
        );
        // uploadedCount++;
        // setUploadProgress(Math.floor((uploadedCount / totalItems) * 100));
      }
    }
    if (uploadWarnings.length > 0) {
      console.warn('Upload warnings:', uploadWarnings);
    }
    return { errors: uploadErrors, warnings: uploadWarnings };
  };

  // const accountDetails = {
  //   bank: 'GTBank',
  //   accountNumber: '**********',
  //   accountName: 'ADE BOLUWATIFE',
  //   location: 'Lekki Conservation Center, Lekki, Lagos State',
  // };

  const submitGiftItems = async () => {
    const currentEventId = selectedEvent?.id;

    if (!currentEventId) {
      // setError('No event selected. Please select an event first.');
      return;
    }

    if (!items || items.length === 0) {
      // setError('No gift items to create. Please add some gift items first.');
      return;
    }

    setIsLoading(true);
    // setError(null);
    // setUploadProgress(0);

    try {
      const payload = {
        gifts: items.map((item) => ({
          name: item.name,
          description: item.description || '',
          price:
            typeof item.price === 'string'
              ? item.price.replace(/,/g, '')
              : String(item.price || 0),
          quantity: item.quantity || 1,
          item_link: item.item_link || '',
        })),
        activate_gift_registry: true,
      };
      const response = await GiftRegistryServices.createItemsGift(
        currentEventId,
        payload
      );
      const createdItems: GiftItemResponse[] = response.data || [];

      if (createdItems.length === 0) {
        throw new Error('No gift items were created by the server');
      }

      if (createdItems.length !== items.length) {
        console.warn(
          `Mismatch in created items count: expected ${items.length}, got ${createdItems.length}`
        );
      }
      const uploadResult = await uploadGiftItemImages(
        createdItems,
        currentEventId
      );
      if (uploadResult.errors.length > 0) {
        uploadResult.errors.forEach((err) => toast.error(err));
      }

      if (uploadResult.warnings.length > 0) {
        console.warn('Image upload warnings:', uploadResult.warnings);
      }
      // const successfulUploads =
      //   createdItems.length - uploadResult.errors.length;
      // const totalWithImages = items.filter((item) => item.image).length;

      // if (successfulUploads > 0) {
      //   toast.success(
      //     `Gift registry created successfully! ${successfulUploads}/${totalWithImages} images uploaded.`
      //   );
      // }

      const mostWantedItem = getMostWantedItem();
      if (mostWantedItem && selectedEvent?.id) {
        try {
          const correspondingCreatedItem = createdItems.find(
            (createdItem) => createdItem.name === mostWantedItem.name
          );

          if (correspondingCreatedItem) {
            await events.updateEventDetails(selectedEvent.id, {
              most_wanted_gift_id: correspondingCreatedItem.id,
            });
          }
        } catch (mostWantedError) {
          console.error(
            'Failed to update most wanted gift ID:',
            mostWantedError
          );
        }
      }

      setOpen(true);
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message ||
        err?.message ||
        'Failed to create gift registry. Please try again.';
      // setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  console.log('se', selectedEvent);
  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0 lg:ml-54">
        <div className="flex justify-center flex-col lg:flex-row items-center lg:items-start mt-7 ">
          <div className="max-w-[560px] w-full ">
            {/* <h1 className="text-[28px] font-semibold italic mb-4 ">
              {initialData.registryTitle || "Oladele's birthday gifts"}
            </h1>

            <div className=" pt-5 px-4 pb-4 rounded-xl bg-[linear-gradient(182.72deg,#FEF7F4_20.31%,#F5F6FE_97.2%)]">
              <div className="text-2xl font-bold mb-1.5">**********</div>
              <div>
                <span className="text-cus-orange-700 font-medium text-sm">
                  {accountDetails.bank}{' '}
                </span>
                <span className="text-grey-950 text-sm italic font-bold">
                  • {accountDetails.accountName}
                </span>
              </div>
              <div className="flex items-center text-sm italic text-dark-blue-200 gap-2 mt-4.5 ">
                <Icon name="bus" />
                <span>{accountDetails.location}</span>
              </div>
            </div> */}
            <h1 className="text-[28px] font-semibold italic mb-4 ">
              {initialData.registryTitle || "Oladele's birthday gifts"}
            </h1>

            <div className="mb-6 bg-light-blue-50 h-[147px] px-5 rounded-[16px] flex  items-center">
              <div className="w-full ">
                <div className="flex items-center justify-between mb-3 w-full">
                  <h2 className="text-sm font-medium text-primary-650 bg-white px-4 py-1.5 rounded-full">
                    Delivery Address
                  </h2>
                  <button className="text-primary-650 text-xs  border border-primary-650 px-3 py-1 rounded-full">
                    Edit Details
                  </button>
                </div>

                <div className="flex items-center text-sm text-grey-600 gap-2 mt-5">
                  <div className="bg-white p-3.5 rounded-full">
                    <TruckTick size={28} variant="Bulk" color="#A6AAF9" />
                  </div>{' '}
                  <span className="font-bold italic text-base text-dark-blue-600 max-w-[280px] w-full">
                    {selectedEvent?.delivery_address || ''}
                  </span>
                </div>
              </div>
            </div>

            {items.length > 0 ? (
              <>
                <div className="my-6 text-primary text-sm bg-primary-250 w-fit px-2.5 py-1 rounded-2xl italic font-bold">
                  {getItemCount()} Gift Items
                </div>

                <div className="space-y-6">
                  {items.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center md:h-[180px] py-3 sm:py-0 flex-col md:flex-row border gap-4 border-grey-150 rounded-[14px] relative">
                      <img
                        src={
                          typeof item.image === 'string'
                            ? item.image
                            : item.image instanceof File
                            ? URL.createObjectURL(item.image)
                            : ''
                        }
                        alt={item.name}
                        className="max-w-[155px] w-full h-full md:rounded-l-[14px]"
                      />
                      <div className="h-full w-full p-2">
                        <div className="flex justify-end ">
                          <button
                            onClick={() => removeGiftItem(item.id)}
                            className="cursor-pointer hover:opacity-70 transition-opacity">
                            <CloseCircle
                              size={28}
                              variant="Bulk"
                              color="#9499F7"
                            />
                          </button>
                        </div>
                        <div>
                          <h2 className="text-[22px] text-grey-750 font-medium ">
                            {item.name}
                          </h2>
                          <p className="text-grey-100 my-1 max-w-[300px] truncate">
                            {item.description}
                          </p>
                        </div>
                        <div className="mt-4 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                          <Tag2 size={12} variant="Bulk" color="#5925DC " />
                          <span className="text-perple text-sm font-medium">
                            ₦
                            {item.price
                              ? typeof item.price === 'string'
                                ? parseFloat(
                                    item.price.replace(/,/g, '')
                                  ).toLocaleString()
                                : Number(item.price).toLocaleString()
                              : '0'}
                          </span>
                        </div>
                        <div className="flex justify-end mb-3">
                          {item.mostWanted === true && (
                            <div className=" bg-primary-150 text-primary-750 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10 italic">
                              📍 MOST WANTED
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="my-6 text-center py-12">
                <div className="text-grey-500 text-lg mb-2">
                  No gift items added yet
                </div>
                <p className="text-grey-400 text-sm">
                  Add some gift items to your registry to see them here.
                </p>
              </div>
            )}
          </div>
          <button
            type="button"
            onClick={submitGiftItems}
            disabled={isLoading || items.length === 0}
            className="bg-primary text-base mt-5 lg:mt-0 font-semibold cursor-pointer text-white rounded-full py-2.5 px-4 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed">
            {isLoading ? 'Creating...' : 'Create Gift Registry'}
            <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
          </button>
        </div>
      </div>
      {open && <Success onClose={onClose} itemCount={items.length} />}
    </>
  );
};
