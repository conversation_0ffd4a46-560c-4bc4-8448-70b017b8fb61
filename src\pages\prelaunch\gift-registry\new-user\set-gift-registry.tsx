import { useState, useEffect } from 'react';
import { SetupGiftRegistry } from './setup-gift-registry';
import { useUserAuthStore } from '../../../../lib/store/auth';
import { useToolStatusRefresh } from '../../../../lib/hooks/useToolStatusRefresh';

export const SetGiftRegistry = ({ onClose }: { onClose?: () => void }) => {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const { toolStatus } = useUserAuthStore();
  const { refreshToolStatus } = useToolStatusRefresh();

  const handleClose = () => {
    // Refresh tool status before closing so when user reopens, they get updated behavior
    refreshToolStatus();
    if (onClose) {
      onClose();
    }
  };

  useEffect(() => {
    if (!toolStatus) return;

    const newCompletedSteps: number[] = [];
    let initialStep = 1;
    if (toolStatus.has_bank_account) {
      newCompletedSteps.push(1);
    }

    if (toolStatus.has_wallet) {
      newCompletedSteps.push(2);
    }

    // Determine the initial step to start from
    // Since users can now skip steps, we need to be more flexible
    if (toolStatus.has_bank_account && toolStatus.has_wallet) {
      // Both completed, go to delivery address
      initialStep = 3;
    } else {
      // If either is missing, start from the beginning and let users skip if they want
      // This allows users to skip wallet/account setup and proceed to delivery address
      initialStep = 1;
    }

    setCompletedSteps(newCompletedSteps);
    setActiveStep(initialStep);
  }, [toolStatus]);

  const handleStepChange = (step: number) => {
    if (step > activeStep) {
      setCompletedSteps((prev) => [...new Set([...prev, activeStep])]);
    }
    setActiveStep(step);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-white [&::-webkit-scrollbar]:hidden">
      <div className="flex flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] min-h-screen relative">
        <div
          className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
          style={{
            backgroundSize: '100% auto',
          }}
        />
        <div className="relative z-10 flex-1">
          <SetupGiftRegistry
            activeStep={activeStep}
            completedSteps={completedSteps}
            onStepChange={handleStepChange}
            onClose={handleClose}
          />
        </div>
      </div>
    </div>
  );
};
