import { useState, useEffect } from 'react';
import { SetupGiftRegistry } from './setup-gift-registry';
import { useUserAuthStore } from '../../../../lib/store/auth';
import { useToolStatusRefresh } from '../../../../lib/hooks/useToolStatusRefresh';

export const SetGiftRegistry = ({ onClose }: { onClose?: () => void }) => {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [visitedSteps, setVisitedSteps] = useState<number[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const { toolStatus } = useUserAuthStore();
  const { refreshToolStatus } = useToolStatusRefresh();

  const handleClose = () => {
    refreshToolStatus();
    if (onClose) {
      onClose();
    }
  };

  useEffect(() => {
    if (!toolStatus || isInitialized) return;

    const newCompletedSteps: number[] = [];
    const newVisitedSteps: number[] = [];
    let initialStep = 1;

    if (toolStatus.has_bank_account) {
      newCompletedSteps.push(1);
    }

    if (toolStatus.has_wallet) {
      newCompletedSteps.push(2);
    }

    if (toolStatus.has_bank_account && toolStatus.has_wallet) {
      initialStep = 3;
    } else {
      initialStep = 1;
    }

    // Mark the initial step as visited
    newVisitedSteps.push(initialStep);

    setCompletedSteps(newCompletedSteps);
    setVisitedSteps(newVisitedSteps);
    setActiveStep(initialStep);
    setIsInitialized(true);
  }, [toolStatus, isInitialized]);

  // Update only completed steps based on tool status
  useEffect(() => {
    if (!toolStatus || !isInitialized) return;

    const toolBasedCompleted: number[] = [];

    if (toolStatus.has_bank_account) {
      toolBasedCompleted.push(1);
    }

    if (toolStatus.has_wallet) {
      toolBasedCompleted.push(2);
    }

    setCompletedSteps(toolBasedCompleted);
  }, [toolStatus, isInitialized]);

  const handleStepChange = (step: number) => {
    // Mark the current step as visited before moving
    setVisitedSteps((prev) => [...new Set([...prev, activeStep])]);

    // Mark the new step as visited
    setVisitedSteps((prev) => [...new Set([...prev, step])]);

    setActiveStep(step);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-white [&::-webkit-scrollbar]:hidden">
      <div className="flex flex-col bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] min-h-screen relative">
        <div
          className="absolute inset-0 bg-[url('/src/assets/images/blur-bg.png')] bg-no-repeat bg-center bg-cover"
          style={{
            backgroundSize: '100% auto',
          }}
        />
        <div className="relative z-10 flex-1">
          <SetupGiftRegistry
            activeStep={activeStep}
            completedSteps={[...new Set([...completedSteps, ...visitedSteps])]}
            toolCompletedSteps={completedSteps}
            onStepChange={handleStepChange}
            onClose={handleClose}
          />
        </div>
      </div>
    </div>
  );
};
