import { useState } from "react";
import {
  GuestGiftsAPI,
  CashGift,
  CashGiftWithMetrics,
} from "../../../../lib/apis/guestGiftsApi";
import { toast } from "react-toastify";

interface CashGiftAmountProps {
  onContinue?: (reservationId?: string) => void;
  cashGift?: CashGift | CashGiftWithMetrics;
  accessToken?: string | null;
  eventDetails?: EventDetails | null;
}

interface EventDetails {
  gift_registry_title: string;
  host_first_name: string;
  host_last_name: string;
  id: string;
}

// Figma-accurate Cash Gift Amount Card
export const CashGiftAmount = ({
  onContinue,
  cashGift,
  accessToken,
  eventDetails,
}: CashGiftAmountProps) => {
  console.log(accessToken);
  const [giftAmount, setGiftAmount] = useState("");
  const [isReserving, setIsReserving] = useState(false);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9,]/g, ""); // Allow commas
    const numericValue = value.replace(/,/g, ""); // Remove commas for processing

    // Format with commas for display
    const formattedValue = formatNumberWithCommas(numericValue);
    setGiftAmount(formattedValue);
  };

  // Helper function to format number with commas
  const formatNumberWithCommas = (value: string) => {
    if (!value) return "";
    return value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatAmount = (amount: string) => {
    if (!amount) return "₦0.00";
    return `₦${new Intl.NumberFormat("en-NG").format(parseInt(amount))}.00`;
  };

  const handleReserveGift = async () => {
    const numericAmount = giftAmount.replace(/,/g, ""); // Remove commas for validation
    if (!numericAmount || parseInt(numericAmount) <= 0) {
      toast.error("Please enter a valid gift amount");
      return;
    }

    if (!cashGift) {
      toast.error("Gift information not available");
      return;
    }

    setIsReserving(true);
    try {
      const reservation = await GuestGiftsAPI.reserveCashGift(cashGift.id, {
        amount: giftAmount.replace(/,/g, "") + ".00", // Remove commas before sending to API
      });
      toast.success("Cash gift reserved successfully!");
      if (onContinue) {
        onContinue(reservation.id);
      }
    } catch (error) {
      const errorMsg =
        typeof error === "object" && error && "message" in error
          ? (error as { message?: string }).message ?? "Failed to reserve gift"
          : "Failed to reserve gift";
      toast.error(errorMsg);
    } finally {
      setIsReserving(false);
    }
  };

  return (
    <div className="w-full flex flex-col pl-2 md:pl-6 items-start pt-8 font-rethink">
      {/* Section Title */}
      <div className="font-medium text-[18px] tracking-[-0.04em] text-[#090909] mb-6 text-left">
        How would you like to gift {eventDetails?.host_first_name}
      </div>
      {/* Card */}
      <div className="bg-gradient-to-b from-[#FAFAFA]  p-5 rounded-[16px] to-[#FFFFFF]">
        <div className="w-full border border-[#E6E6E6] bg-white rounded-[16px]  border-dashed  shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] p-6 flex flex-col gap-4">
          {/* Header Row */}
          <div className="flex items-center gap-2 mb-2">
            <span className="w-6 h-6 flex items-center justify-center rounded-full bg-[#E6E6E6]">
              {/* Truck-tick SVG */}
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width="16" height="16" rx="8" fill="#E6E6E6" />
                <path
                  opacity="0.4"
                  d="M9.33594 2.66699V7.99935C9.33594 8.48868 8.95594 8.86868 8.4666 8.86868H3.5566V6.05268C3.88194 6.43935 4.37927 6.66868 4.92927 6.66868C5.37927 6.66868 5.7826 6.48868 6.08927 6.18868C6.22327 6.06135 6.33594 5.90868 6.41594 5.74135C6.5886 5.43468 6.66927 5.08135 6.66927 4.72135C6.66927 4.08135 6.26927 3.52135 5.66927 3.33468H8.66927C9.0226 3.33468 9.33594 3.64801 9.33594 4.00135V2.66699Z"
                  fill="#292D32"
                />
                <path
                  d="M12.6693 8.86868V10.0013C12.6693 10.9593 11.9593 11.6687 11.0013 11.6687H10.6693C10.6693 11.1793 10.2893 10.7993 9.79927 10.7993C9.30927 10.7993 8.92927 11.1793 8.92927 11.6687H6.66927C6.66927 11.1793 6.28927 10.7993 5.79927 10.7993C5.30927 10.7993 4.92927 11.1793 4.92927 11.6687H4.66927C3.71127 11.6687 3.00127 10.9593 3.00127 10.0013V8.86868H8.4666C8.95594 8.86868 9.33594 8.48868 9.33594 7.99935V4.00135H10.1526C10.4726 4.00135 10.7593 4.18135 10.9193 4.46868L11.6866 5.86868H11.0013C10.6479 5.86868 10.3359 6.18068 10.3359 6.53401V8.53401C10.3359 8.88735 10.6479 9.19935 11.0013 9.19935H12.6693Z"
                  fill="#292D32"
                />
                <path
                  opacity="0.4"
                  d="M6.66667 12.6667C7.37905 12.6667 7.96667 12.0791 7.96667 11.3667C7.96667 10.6543 7.37905 10.0667 6.66667 10.0667C5.95429 10.0667 5.36667 10.6543 5.36667 11.3667C5.36667 12.0791 5.95429 12.6667 6.66667 12.6667Z"
                  fill="#292D32"
                />
                <path
                  opacity="0.4"
                  d="M9.79927 12.6667C10.5117 12.6667 11.0993 12.0791 11.0993 11.3667C11.0993 10.6543 10.5117 10.0667 9.79927 10.0667C9.08689 10.0667 8.49927 10.6543 8.49927 11.3667C8.49927 12.0791 9.08689 12.6667 9.79927 12.6667Z"
                  fill="#292D32"
                />
              </svg>
            </span>
            <span className="font-medium text-[12px] text-[#8E8E93] tracking-[0.12em] uppercase">
              CONTRIBUTE TO THE CAUSE
            </span>
          </div>

          <div className="flex flex-col gap-1 mb-2">
            <span className="font-normal text-[12px] text-[#8E8E93] tracking-[-0.02em]">
              Amount
            </span>
            <span className="font-bold text-[24px] text-[#090909] tracking-[-0.025em]">
              {formatAmount(giftAmount)}
            </span>
          </div>

          {/* Description */}
          <div className="mb-2">
            <span className="font-normal text-[14px] text-[#666] tracking-[-0.03em] leading-[1.6] italic">
              Your payment is securely processed through a trusted gateway,
              ensuring a smooth and safe gift contribution.
            </span>
          </div>
          {/* Amount Section */}

          {/* Input Field */}
          <div className="mb-3">
            <label
              className="font-medium text-[14px] text-[#414651] mb-1 block"
              htmlFor="gift-amount-input"
            >
              Gift Amount
            </label>
            <input
              id="gift-amount-input"
              type="text"
              value={giftAmount}
              onChange={handleAmountChange}
              placeholder="Input amount you want to gift"
              className="w-full h-12 rounded-full border border-[#D5D7DA] px-4 text-[16px] text-[#717680] outline-none mt-0.5 font-rethink placeholder-[#717680]"
              autoComplete="off"
              inputMode="numeric"
            />
          </div>
        </div>
        {/* Reserve Gift Button */}
        <button
          onClick={handleReserveGift}
          disabled={
            isReserving ||
            !giftAmount ||
            parseInt(giftAmount.replace(/,/g, "")) <= 0
          }
          className="flex mt-5 items-center gap-2 bg-[#4D55F2] text-white font-rethink font-semibold text-[14px] rounded-full px-6 py-3 shadow-sm hover:bg-[#343CD8] transition-all w-fit self-start disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isReserving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Reserving...
            </>
          ) : (
            <>
              Reserve Gift
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                  fill="white"
                />
                <path
                  d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                  fill="white"
                />
              </svg>
            </>
          )}
        </button>
      </div>
    </div>
  );
};
