/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { Button } from "../../components/button/button";
import { OTPInput } from "../../components/inputs/otp-input/otp-input";
import { useMutation } from "@tanstack/react-query";
import { AuthServices } from "../../lib/services/auth";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../lib/store/auth";
import { formatTime } from "../../lib/helpers";

export function StepTwo({
  handleNext,
  email,
  first_name,
  last_name,
  expiresAt,
}: {
  handleNext: VoidFunction;
  email: string;
  first_name: string;
  last_name: string;
  expiresAt: string;
}) {
  const [otp, setOtp] = useState("");
  const setAuthData = useUserAuthStore((state) => state.setAuthData);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [canResend, setCanResend] = useState(false);
  const startCountdown = (expiration: string) => {
    const expirationTime = new Date(expiration).getTime();
    const now = new Date().getTime();
    const remainingTime = Math.max(
      0,
      Math.floor((expirationTime - now) / 1000)
    );

    setTimeLeft(remainingTime);
    setCanResend(remainingTime <= 0);

    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  };
  useEffect(() => {
    if (!expiresAt) return;
    return startCountdown(expiresAt);
  }, [expiresAt]);
  const verifyMutation = useMutation({
    mutationFn: () =>
      AuthServices.verifyRegistrationOTP({
        otp: otp,
        email: email,
      }),
    onSuccess: (data) => {
      const access_token = data?.data?.access_token;
      const refresh_token = data?.data?.refresh_token;
      const access_token_expires_at = data?.data?.access_token_expires_at;
      const refresh_token_expires_at = data?.data?.refresh_token_expires_at;

      setAuthData(
        access_token,
        {
          email,
          first_name,
          last_name,
          id: "",
          profile_picture: "",
          password_set: false,
          transaction_pin_set: false,
        },
        refresh_token,
        access_token_expires_at,
        refresh_token_expires_at
      );
      handleNext();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const resendMutation = useMutation({
    mutationFn: () =>
      AuthServices.initiateRegistration({
        first_name: first_name,
        last_name: last_name,
        email: email,
      }),
    onSuccess: (data) => {
      const newExpiresAt = data?.data?.expires_at;
      if (newExpiresAt) {
        startCountdown(newExpiresAt);
        setCanResend(false);
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || error?.message);
    },
  });
  const handleOTP = () => {
    console.log("email", email, expiresAt);
    verifyMutation.mutate();
  };
  const handleResendOTP = () => {
    if (canResend) {
      resendMutation.mutate();
    }
  };
  return (
    <div>
      <div className="mb-[4vh]">
        <h1 className="font-semibold text-[32px]">Verify your Account</h1>
        <h3 className="text-grey-100 mb-9">
          Check your mail for a 6-Digit otp sent to verify your email address to
          continue with account creation
        </h3>

        <OTPInput value={otp} onChange={setOtp} className="mb-6" />
        <div className="flex items-center gap-3 border border-grey-800 py-2 pr-1 pl-3.5 rounded-full mb-14 max-w-[218px] text-sm font-medium">
          <span className="text-grey-100 leading-none text-nowrap">
            No OTP Yet?
          </span>
          <Button
            onClick={handleResendOTP}
            variant="neutral"
            disabled={!canResend || resendMutation.isPending}
            className={`bg-cus-pink-300 h-7 px-3 text-nowrap  items-center text-primary-650 rounded-full ${
              !canResend && "text-xs !cursor-default font-bold "
            }`}
          >
            {canResend ? "Resend OTP" : `Resend in ${formatTime(timeLeft)}`}
          </Button>
        </div>
        <Button
          onClick={handleOTP}
          type="button"
          variant="primary"
          isLoading={verifyMutation.isPending}
          disabled={otp.length < 6 || verifyMutation.isPending}
        >
          Verify your Account
        </Button>
      </div>
    </div>
  );
}
