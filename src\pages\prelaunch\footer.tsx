import { Link } from "react-router-dom";
import logo from "../../assets/icons/Ep-Logo.svg";
import { SOCIAL_LINKS } from "../../lib/constants";

export const Footer = () => {
  return (
    <div className="border-t border-grey-150 py-6  flex md:flex-row flex-col items-center justify-around lg:justify-start lg:pl-30 lg:gap-65">
      <Link to="/">
        <img src={logo} alt="logo" />
      </Link>{" "}
      <div className="flex justify-center flex-row items-center md:space-x-6 space-x-3 text-xs sm:text-sm text-grey-550 font-light mt-5 md:mt-0">
        <a
          href={SOCIAL_LINKS.INSTAGRAM}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-primary transition-colors "
        >
          INSTAGRAM
        </a>
        <span className="text-lg text-primary-200">•</span>
        <a
          href={SOCIAL_LINKS.TWITTER}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-primary transition-colors"
        >
          X(TWITTER)
        </a>
        <span className="text-lg text-primary-200">•</span>
        <a
          href={SOCIAL_LINKS.TIKTOK}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-primary transition-colors"
        >
          TIKTOK
        </a>
        <span className="text-lg text-primary-200">•</span>
        <a
          href={SOCIAL_LINKS.LINKEDIN}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-primary transition-colors"
        >
          LINKEDIN
        </a>
      </div>
    </div>
  );
};
