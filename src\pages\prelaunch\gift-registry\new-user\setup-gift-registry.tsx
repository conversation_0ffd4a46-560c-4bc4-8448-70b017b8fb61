import { Stepper } from '../../../../components/reuseables/stepper';
import { AccountSetup } from './account-setup';
import { WalletSetup } from './wallet-setup';
import { DeliveryAddress } from './delivery-address';
import { AllSet } from './all-set';
import { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import { useUserAuthStore } from '../../../../lib/store/auth';

interface RegistryData {
  bank?: string;
  accountNumber?: string;
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
}

export const SetupGiftRegistry = ({
  activeStep,
  completedSteps = [],
  onStepChange,
  onClose,
}: {
  activeStep: number;
  completedSteps: number[];
  onStepChange: (step: number) => void;
  onClose?: () => void;
}) => {
  const [registryData, setRegistryData] = useState<RegistryData>({});
  // const navigate = useNavigate();
  const { toolStatus } = useUserAuthStore();
  const steps = [
    { id: 1, name: 'Account Setup' },
    { id: 2, name: 'Wallet Setup' },
    { id: 3, name: 'Delivery Address' },
    { id: 4, name: 'All Set' },
  ];

  const handleNextStep = (data: Partial<RegistryData>) => {
    setRegistryData((prev) => ({ ...prev, ...data }));

    // Determine the next step based on current step and user's setup status
    let nextStep = activeStep + 1;

    // If we're on step 1 (Account Setup) and user already has wallet, skip to step 3
    if (activeStep === 1 && toolStatus?.has_wallet) {
      nextStep = 3;
    }
    // If we're on step 2 (Wallet Setup) and user already has bank account, we shouldn't be here
    // but if we are, go to step 3
    else if (activeStep === 2) {
      nextStep = 3;
    }

    onStepChange(nextStep);
  };

  const handleStepChange = (step: number) => {
    if (step === 1 && toolStatus?.has_bank_account) {
      return;
    }
    if (step === 2 && toolStatus?.has_wallet) {
      return;
    }
    onStepChange(step);
  };

  // const handleComplete = () => {
  //   if (onClose) {
  //     onClose();
  //   }
  // };
  const handleNext = () => {
    if (onClose) {
      onClose();
    }
  };
  const disabledSteps = [
    ...(toolStatus?.has_bank_account ? [1] : []),
    ...(toolStatus?.has_wallet ? [2] : []),
  ];

  return (
    <div>
      <Stepper
        steps={steps}
        activeStep={activeStep}
        completedSteps={completedSteps}
        disabledSteps={disabledSteps}
        title="Create Gift Registry"
        onStepChange={handleStepChange}
        onClose={handleNext}
      />

      {activeStep === 1 && !toolStatus?.has_bank_account && (
        <AccountSetup onNextStep={handleNextStep} initialData={registryData} />
      )}

      {activeStep === 2 && !toolStatus?.has_wallet && (
        <WalletSetup onNextStep={() => handleNextStep({})} />
      )}

      {activeStep === 3 && (
        <DeliveryAddress
          onNextStep={handleNextStep}
          initialData={registryData}
        />
      )}

      {activeStep === 4 && (
        <AllSet
        // onComplete={handleComplete}
        />
      )}
    </div>
  );
};
