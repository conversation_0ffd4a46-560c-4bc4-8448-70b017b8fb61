import { Stepper } from '../../../../components/reuseables/stepper';
import { AccountSetup } from './account-setup';
import { WalletSetup } from './wallet-setup';
import { DeliveryAddress } from './delivery-address';
import { AllSet } from './all-set';
import { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import { useUserAuthStore } from '../../../../lib/store/auth';

interface RegistryData {
  bank?: string;
  accountNumber?: string;
  state?: string;
  city?: string;
  address?: string;
  shareAddress?: boolean;
}

export const SetupGiftRegistry = ({
  activeStep,
  completedSteps = [],
  onStepChange,
  onClose,
}: {
  activeStep: number;
  completedSteps: number[];
  onStepChange: (step: number) => void;
  onClose?: () => void;
}) => {
  const [registryData, setRegistryData] = useState<RegistryData>({});
  // const navigate = useNavigate();
  const { toolStatus } = useUserAuthStore();
  const steps = [
    { id: 1, name: 'Account Setup' },
    { id: 2, name: 'Wallet Setup' },
    { id: 3, name: 'Delivery Address' },
    { id: 4, name: 'All Set' },
  ];

  const handleNextStep = (data: Partial<RegistryData>) => {
    setRegistryData((prev) => ({ ...prev, ...data }));

    // Determine the next step based on current step
    let nextStep = activeStep + 1;

    // Since users can now skip steps, we allow normal progression
    // The skip functionality is handled within each individual component
    // If user skips account setup (step 1), they go to wallet setup (step 2)
    // If user skips wallet setup (step 2), they go to delivery address (step 3)
    // If user already has wallet and we're on step 1, skip to step 3
    if (activeStep === 1 && toolStatus?.has_wallet) {
      nextStep = 3;
    }

    onStepChange(nextStep);
  };

  const handleStepChange = (step: number) => {
    // Allow users to navigate to any step freely
    // They can choose to fill it out or skip it once they're there
    onStepChange(step);
  };

  // const handleComplete = () => {
  //   if (onClose) {
  //     onClose();
  //   }
  // };
  const handleNext = () => {
    if (onClose) {
      onClose();
    }
  };
  // Don't disable any steps - let users navigate freely
  // They can choose to skip or fill out any step they navigate to
  const disabledSteps: number[] = [];

  return (
    <div>
      <Stepper
        steps={steps}
        activeStep={activeStep}
        completedSteps={completedSteps}
        disabledSteps={disabledSteps}
        title="Create Gift Registry"
        onStepChange={handleStepChange}
        onClose={handleNext}
      />

      {activeStep === 1 && (
        <>
          {toolStatus?.has_bank_account ? (
            <div className="px-4 md:px-0 md:ml-3.5">
              <div className="max-w-[550px] mx-auto mb-32 mt-9">
                <h2 className="text-2xl md:text-[40px] font-medium mb-4">
                  Account Setup Complete
                </h2>
                <p className="text-grey-500 mb-8">
                  You have already set up your bank account details.
                </p>
                <button
                  onClick={() => handleNextStep({})}
                  className="bg-primary-650 text-white py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2">
                  Continue to Next Step
                  <div className="bg-white/30 rounded-full p-0.5">
                    <ArrowRight size="12" color="#fff" />
                  </div>
                </button>
              </div>
            </div>
          ) : (
            <AccountSetup
              onNextStep={handleNextStep}
              initialData={registryData}
            />
          )}
        </>
      )}

      {activeStep === 2 && (
        <>
          {toolStatus?.has_wallet ? (
            <div className="px-4 md:px-0 md:ml-3.5">
              <div className="max-w-[550px] mx-auto mb-32 mt-9">
                <h2 className="text-2xl md:text-[40px] font-medium mb-4">
                  Wallet Setup Complete
                </h2>
                <p className="text-grey-500 mb-8">
                  You have already set up your wallet.
                </p>
                <button
                  onClick={() => handleNextStep({})}
                  className="bg-primary-650 text-white py-2.5 px-4 rounded-full cursor-pointer flex items-center gap-2">
                  Continue to Next Step
                  <div className="bg-white/30 rounded-full p-0.5">
                    <ArrowRight size="12" color="#fff" />
                  </div>
                </button>
              </div>
            </div>
          ) : (
            <WalletSetup onNextStep={() => handleNextStep({})} />
          )}
        </>
      )}

      {activeStep === 3 && (
        <DeliveryAddress
          onNextStep={handleNextStep}
          initialData={registryData}
        />
      )}

      {activeStep === 4 && (
        <AllSet
        // onComplete={handleComplete}
        />
      )}
    </div>
  );
};
