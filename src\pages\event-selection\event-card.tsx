import { format } from 'date-fns';
import { CreatedEventData } from '../../lib/store/event';
interface EventCardProps {
  event: CreatedEventData;
  onSelect: (eventId: string) => void;
}

export const EventCard = ({ event, onSelect }: EventCardProps) => {
  const formatEventDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'do MMMM yyyy').toUpperCase();
    } catch (error) {
      console.log(error);
      return 'DATE NOT SET';
    }
  };

  const getProgressBarColor = () => {
    const colors = [
      '#5F66F3', 
      '#4F46E5', 
      '#FF5519', 
    ];

    const colorIndex = parseInt(event.id.slice(-1), 16) % colors.length;
    return colors[colorIndex];
  };

  return (
    <div
      onClick={() => onSelect(event.id)}
      className="relative bg-white p-1 rounded-2xl overflow-hidden cursor-pointer transition-all duration-200 hover:scale-[1.02] hover:shadow-lg group w-full max-w-sm">
      <div className="relative">
        <img src={event?.banner_preview_url || ''} alt={event.title} className="w-full rounded-2xl h-48 object-cover" />
        <div className="absolute bottom-1 mx-1 left-0 right-0 py-1 rounded-full backdrop-blur-sm bg-white">
          <div
            className="h-5 w-1/3 ml-1 rounded-full transition-all duration-300 "
            style={{ backgroundColor: getProgressBarColor() }}
          />
        </div>
      </div>

      <div className="p-4 bg-white">
        <h3 className="font-semibold text-[#090909] capitalize text-xl mb-2 leading-tight">
          {event.title}
        </h3>

        <div className="flex items-center gap-1 text-red-500">
          <span className="text-xs text-[#666666] font-medium uppercase tracking-[0.14em]">
            📌 {formatEventDate(event.date_from)}
          </span>
        </div>
      </div>
    </div>
  );
};
